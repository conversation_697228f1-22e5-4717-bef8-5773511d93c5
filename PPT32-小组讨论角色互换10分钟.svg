<?xml version="1.0" encoding="UTF-8"?>
<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="#ffffff"/>
  
  <!-- 装饰性弧线背景 -->
  <path d="M0,100 Q960,50 1920,100" stroke="#e3f2fd" stroke-width="25" fill="none" opacity="0.3"/>
  
  <!-- 标题 -->
  <text x="960" y="120" text-anchor="middle" font-family="Microsoft YaHei" font-size="56" font-weight="bold" fill="#1565c0">
    小组讨论 (10分钟)
  </text>
  
  <!-- 大问号图标 -->
  <g transform="translate(960,300)">
    <circle cx="0" cy="0" r="100" fill="#1565c0" opacity="0.1"/>
    <text x="0" y="30" text-anchor="middle" font-family="Microsoft YaHei" font-size="150" font-weight="bold" fill="#1565c0">?</text>
  </g>
  
  <!-- 讨论任务 -->
  <g transform="translate(200,500)">
    <rect x="0" y="0" width="1520" height="400" rx="20" fill="#e3f2fd" opacity="0.2"/>
    <text x="760" y="50" text-anchor="middle" font-family="Microsoft YaHei" font-size="40" font-weight="bold" fill="#1565c0">讨论任务："角色互换"</text>
    
    <circle cx="80" cy="120" r="8" fill="#1565c0"/>
    <text x="110" y="130" font-family="Microsoft YaHei" font-size="28" fill="#424242">假设，你们是小李的竞争对手（电信/联通），</text>
    <text x="110" y="165" font-family="Microsoft YaHei" font-size="28" fill="#424242">当时你们也接到了这个客户"宿舍网络改造"的需求。</text>
    
    <circle cx="80" cy="220" r="8" fill="#1565c0"/>
    <text x="110" y="230" font-family="Microsoft YaHei" font-size="28" fill="#424242">请问，你们会怎么做？</text>
    
    <circle cx="80" cy="280" r="8" fill="#1565c0"/>
    <text x="110" y="290" font-family="Microsoft YaHei" font-size="28" fill="#424242">对比小李的做法，你们认为，</text>
    <text x="110" y="325" font-family="Microsoft YaHei" font-size="28" fill="#424242">你们最终输在了哪里？</text>
  </g>
  
  <!-- 右下角倒计时器 -->
  <g transform="translate(1650,900)">
    <circle cx="0" cy="0" r="80" fill="#ff5722" opacity="0.8"/>
    <circle cx="0" cy="0" r="65" fill="#ffffff"/>
    <text x="0" y="-15" text-anchor="middle" font-family="Microsoft YaHei" font-size="32" font-weight="bold" fill="#ff5722">10</text>
    <text x="0" y="15" text-anchor="middle" font-family="Microsoft YaHei" font-size="24" fill="#ff5722">分钟</text>
    
    <!-- 时钟指针 -->
    <line x1="0" y1="0" x2="0" y2="-40" stroke="#ff5722" stroke-width="4"/>
    <line x1="0" y1="0" x2="25" y2="0" stroke="#ff5722" stroke-width="3"/>
    <circle cx="0" cy="0" r="4" fill="#ff5722"/>
  </g>
  
  <!-- 装饰性思考泡泡 -->
  <g transform="translate(300,200)" opacity="0.3">
    <circle cx="0" cy="0" r="15" fill="#1565c0"/>
    <circle cx="25" cy="-10" r="20" fill="#1565c0"/>
    <circle cx="55" cy="-25" r="30" fill="#1565c0"/>
  </g>
  
  <g transform="translate(1400,200)" opacity="0.3">
    <circle cx="0" cy="0" r="15" fill="#1565c0"/>
    <circle cx="-25" cy="-10" r="20" fill="#1565c0"/>
    <circle cx="-55" cy="-25" r="30" fill="#1565c0"/>
  </g>
  
  <!-- 底部装饰弧线 -->
  <path d="M0,950 Q960,900 1920,950" stroke="#e3f2fd" stroke-width="20" fill="none" opacity="0.4"/>
</svg>
