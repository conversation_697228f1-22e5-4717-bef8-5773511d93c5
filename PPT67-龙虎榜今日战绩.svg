<?xml version="1.0" encoding="UTF-8"?>
<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="#ffffff"/>
  
  <!-- 装饰性弧线背景 -->
  <path d="M0,100 Q960,50 1920,100" stroke="#fff3e0" stroke-width="25" fill="none" opacity="0.3"/>
  
  <!-- 标题 -->
  <text x="960" y="120" text-anchor="middle" font-family="Microsoft YaHei" font-size="56" font-weight="bold" fill="#f57c00">
    龙虎榜：今日战绩
  </text>
  
  <!-- 龙虎榜表格 -->
  <g transform="translate(300,250)">
    <rect x="0" y="0" width="1320" height="500" rx="20" fill="#fff3e0" opacity="0.2" stroke="#f57c00" stroke-width="3"/>
    
    <!-- 表头 -->
    <rect x="0" y="0" width="1320" height="80" fill="#f57c00" opacity="0.8"/>
    
    <!-- 表头文字 -->
    <text x="100" y="50" text-anchor="middle" font-family="Microsoft YaHei" font-size="24" font-weight="bold" fill="#ffffff">排名</text>
    <text x="350" y="50" text-anchor="middle" font-family="Microsoft YaHei" font-size="24" font-weight="bold" fill="#ffffff">战队名称</text>
    <text x="600" y="50" text-anchor="middle" font-family="Microsoft YaHei" font-size="24" font-weight="bold" fill="#ffffff">基础分</text>
    <text x="850" y="50" text-anchor="middle" font-family="Microsoft YaHei" font-size="24" font-weight="bold" fill="#ffffff">创新分</text>
    <text x="1100" y="50" text-anchor="middle" font-family="Microsoft YaHei" font-size="24" font-weight="bold" fill="#ffffff">总积分</text>
    
    <!-- 表格分割线 -->
    <line x1="200" y1="0" x2="200" y2="500" stroke="#f57c00" stroke-width="2"/>
    <line x1="500" y1="0" x2="500" y2="500" stroke="#f57c00" stroke-width="2"/>
    <line x1="750" y1="0" x2="750" y2="500" stroke="#f57c00" stroke-width="2"/>
    <line x1="1000" y1="0" x2="1000" y2="500" stroke="#f57c00" stroke-width="2"/>
    <line x1="0" y1="80" x2="1320" y2="80" stroke="#f57c00" stroke-width="2"/>
    
    <!-- 示例行 -->
    <g transform="translate(0,100)">
      <!-- 第一名 -->
      <g transform="translate(0,0)">
        <rect x="0" y="0" width="1320" height="60" fill="#ffd700" opacity="0.2"/>
        <text x="100" y="35" text-anchor="middle" font-family="Microsoft YaHei" font-size="28" font-weight="bold" fill="#f57c00">1</text>
        <text x="350" y="35" text-anchor="middle" font-family="Microsoft YaHei" font-size="24" font-weight="bold" fill="#424242">待填写</text>
        <text x="600" y="35" text-anchor="middle" font-family="Microsoft YaHei" font-size="24" fill="#424242">--</text>
        <text x="850" y="35" text-anchor="middle" font-family="Microsoft YaHei" font-size="24" fill="#424242">--</text>
        <text x="1100" y="35" text-anchor="middle" font-family="Microsoft YaHei" font-size="24" font-weight="bold" fill="#f57c00">--</text>
      </g>
      
      <!-- 第二名 -->
      <g transform="translate(0,80)">
        <rect x="0" y="0" width="1320" height="60" fill="#e0e0e0" opacity="0.2"/>
        <text x="100" y="35" text-anchor="middle" font-family="Microsoft YaHei" font-size="28" font-weight="bold" fill="#757575">2</text>
        <text x="350" y="35" text-anchor="middle" font-family="Microsoft YaHei" font-size="24" font-weight="bold" fill="#424242">待填写</text>
        <text x="600" y="35" text-anchor="middle" font-family="Microsoft YaHei" font-size="24" fill="#424242">--</text>
        <text x="850" y="35" text-anchor="middle" font-family="Microsoft YaHei" font-size="24" fill="#424242">--</text>
        <text x="1100" y="35" text-anchor="middle" font-family="Microsoft YaHei" font-size="24" font-weight="bold" fill="#757575">--</text>
      </g>
      
      <!-- 第三名 -->
      <g transform="translate(0,160)">
        <rect x="0" y="0" width="1320" height="60" fill="#8d6e63" opacity="0.2"/>
        <text x="100" y="35" text-anchor="middle" font-family="Microsoft YaHei" font-size="28" font-weight="bold" fill="#5d4037">3</text>
        <text x="350" y="35" text-anchor="middle" font-family="Microsoft YaHei" font-size="24" font-weight="bold" fill="#424242">待填写</text>
        <text x="600" y="35" text-anchor="middle" font-family="Microsoft YaHei" font-size="24" fill="#424242">--</text>
        <text x="850" y="35" text-anchor="middle" font-family="Microsoft YaHei" font-size="24" fill="#424242">--</text>
        <text x="1100" y="35" text-anchor="middle" font-family="Microsoft YaHei" font-size="24" font-weight="bold" fill="#5d4037">--</text>
      </g>
      
      <!-- 省略号 -->
      <text x="660" y="280" text-anchor="middle" font-family="Microsoft YaHei" font-size="36" fill="#424242">...</text>
    </g>
  </g>
  
  <!-- 装饰性奖杯 -->
  <g transform="translate(150,400)" opacity="0.4">
    <ellipse cx="0" cy="-20" rx="30" ry="20" fill="#ffd700"/>
    <rect x="-25" y="-20" width="50" height="40" fill="#ffd700"/>
    <rect x="-20" y="20" width="40" height="20" fill="#ff9800"/>
    <rect x="-15" y="40" width="30" height="15" fill="#ff8f00"/>
  </g>
  
  <g transform="translate(1770,400)" opacity="0.4">
    <ellipse cx="0" cy="-20" rx="30" ry="20" fill="#ffd700"/>
    <rect x="-25" y="-20" width="50" height="40" fill="#ffd700"/>
    <rect x="-20" y="20" width="40" height="20" fill="#ff9800"/>
    <rect x="-15" y="40" width="30" height="15" fill="#ff8f00"/>
  </g>
  
  <!-- 底部说明 -->
  <g transform="translate(960,850)">
    <rect x="-500" y="-40" width="1000" height="80" rx="15" fill="#fff3e0" opacity="0.3"/>
    <text x="0" y="-5" text-anchor="middle" font-family="Microsoft YaHei" font-size="28" font-weight="bold" fill="#f57c00">
      实时更新中...更激动人心的"创新分"和"地图质量分"即将产生！
    </text>
  </g>
</svg>
