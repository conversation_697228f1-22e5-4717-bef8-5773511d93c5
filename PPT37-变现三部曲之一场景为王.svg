<?xml version="1.0" encoding="UTF-8"?>
<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="#ffffff"/>
  
  <!-- 装饰性弧线背景 -->
  <path d="M0,100 Q960,50 1920,100" stroke="#f3e5f5" stroke-width="25" fill="none" opacity="0.3"/>
  
  <!-- 标题 -->
  <text x="960" y="100" text-anchor="middle" font-family="Microsoft YaHei" font-size="56" font-weight="bold" fill="#7b1fa2">
    变现三部曲之一：场景为王
  </text>
  
  <!-- 副标题 -->
  <text x="960" y="150" text-anchor="middle" font-family="Microsoft YaHei" font-size="36" fill="#424242">
    发现商机的"场景团购魔方"
  </text>
  
  <!-- 中央3D魔方 -->
  <g transform="translate(960,400)">
    <!-- 魔方主体 - 前面 -->
    <rect x="-100" y="-100" width="200" height="200" fill="#e91e63" opacity="0.8" stroke="#ad1457" stroke-width="3"/>
    <line x1="-100" y1="-33" x2="100" y2="-33" stroke="#ad1457" stroke-width="2"/>
    <line x1="-100" y1="33" x2="100" y2="33" stroke="#ad1457" stroke-width="2"/>
    <line x1="-33" y1="-100" x2="-33" y2="100" stroke="#ad1457" stroke-width="2"/>
    <line x1="33" y1="-100" x2="33" y2="100" stroke="#ad1457" stroke-width="2"/>
    
    <!-- 魔方主体 - 右面 -->
    <polygon points="100,-100 150,-150 150,50 100,100" fill="#c2185b" opacity="0.8" stroke="#ad1457" stroke-width="3"/>
    <line x1="100" y1="-33" x2="150" y2="-83" stroke="#ad1457" stroke-width="2"/>
    <line x1="100" y1="33" x2="150" y2="-17" stroke="#ad1457" stroke-width="2"/>
    <line x1="133" y1="-100" x2="133" y2="50" stroke="#ad1457" stroke-width="2"/>
    
    <!-- 魔方主体 - 上面 -->
    <polygon points="-100,-100 -50,-150 150,-150 100,-100" fill="#f06292" opacity="0.8" stroke="#ad1457" stroke-width="3"/>
    <line x1="-33" y1="-100" x2="17" y2="-150" stroke="#ad1457" stroke-width="2"/>
    <line x1="33" y1="-100" x2="83" y2="-150" stroke="#ad1457" stroke-width="2"/>
    <line x1="-50" y1="-133" x2="150" y2="-133" stroke="#ad1457" stroke-width="2"/>
    
    <!-- 魔方标注 -->
    <text x="-200" y="-120" font-family="Microsoft YaHei" font-size="24" font-weight="bold" fill="#7b1fa2">X轴：客户群体</text>
    <text x="180" y="0" font-family="Microsoft YaHei" font-size="24" font-weight="bold" fill="#7b1fa2">Y轴：业务环节</text>
    <text x="-50" y="-180" font-family="Microsoft YaHei" font-size="24" font-weight="bold" fill="#7b1fa2">Z轴：生活时刻</text>
  </g>
  
  <!-- X轴详细说明 -->
  <g transform="translate(300,600)">
    <rect x="0" y="0" width="400" height="150" rx="15" fill="#e91e63" opacity="0.1"/>
    <text x="200" y="30" text-anchor="middle" font-family="Microsoft YaHei" font-size="28" font-weight="bold" fill="#e91e63">X轴：客户群体</text>
    <text x="50" y="70" font-family="Microsoft YaHei" font-size="20" fill="#424242">新员工、销售团队、技术人员、</text>
    <text x="50" y="95" font-family="Microsoft YaHei" font-size="20" fill="#424242">管理者、行政/工会…</text>
  </g>
  
  <!-- Y轴详细说明 -->
  <g transform="translate(760,600)">
    <rect x="0" y="0" width="400" height="150" rx="15" fill="#c2185b" opacity="0.1"/>
    <text x="200" y="30" text-anchor="middle" font-family="Microsoft YaHei" font-size="28" font-weight="bold" fill="#c2185b">Y轴：业务环节</text>
    <text x="50" y="70" font-family="Microsoft YaHei" font-size="20" fill="#424242">内部办公、生产制造、客户营销、</text>
    <text x="50" y="95" font-family="Microsoft YaHei" font-size="20" fill="#424242">人才发展…</text>
  </g>
  
  <!-- Z轴详细说明 -->
  <g transform="translate(1220,600)">
    <rect x="0" y="0" width="400" height="150" rx="15" fill="#f06292" opacity="0.1"/>
    <text x="200" y="30" text-anchor="middle" font-family="Microsoft YaHei" font-size="28" font-weight="bold" fill="#f06292">Z轴：生活时刻</text>
    <text x="50" y="70" font-family="Microsoft YaHei" font-size="20" fill="#424242">节日福利、个人成长、家庭生活、</text>
    <text x="50" y="95" font-family="Microsoft YaHei" font-size="20" fill="#424242">社交出行…</text>
  </g>
  
  <!-- 公式 -->
  <g transform="translate(960,850)">
    <rect x="-500" y="-50" width="1000" height="100" rx="15" fill="#f3e5f5" opacity="0.3"/>
    <text x="0" y="-10" text-anchor="middle" font-family="Microsoft YaHei" font-size="32" font-weight="bold" fill="#7b1fa2">
      客户群体 × 业务环节 × 生活时刻 = 无限的营销场景
    </text>
  </g>
  
  <!-- 装饰性旋转箭头 -->
  <g transform="translate(960,400)" opacity="0.4">
    <path d="M-150,-150 Q-200,-100 -150,-50" stroke="#7b1fa2" stroke-width="3" fill="none" marker-end="url(#rotateArrow)"/>
    <path d="M150,150 Q200,100 150,50" stroke="#7b1fa2" stroke-width="3" fill="none" marker-end="url(#rotateArrow)"/>
  </g>
  
  <!-- 箭头标记定义 -->
  <defs>
    <marker id="rotateArrow" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#7b1fa2"/>
    </marker>
  </defs>
</svg>
