<?xml version="1.0" encoding="UTF-8"?>
<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="#ffffff"/>
  
  <!-- 装饰性弧线背景 -->
  <path d="M0,100 Q960,50 1920,100" stroke="#e8eaf6" stroke-width="25" fill="none" opacity="0.3"/>
  
  <!-- 标题 -->
  <text x="960" y="120" text-anchor="middle" font-family="Microsoft YaHei" font-size="64" font-weight="bold" fill="#3f51b5">
    小组讨论 (15分钟)
  </text>
  
  <!-- 大问号图标 -->
  <g transform="translate(960,300)">
    <circle cx="0" cy="0" r="80" fill="#3f51b5" opacity="0.1"/>
    <text x="0" y="20" text-anchor="middle" font-family="Microsoft YaHei" font-size="120" font-weight="bold" fill="#3f51b5">?</text>
  </g>
  
  <!-- 讨论任务 -->
  <g transform="translate(200,450)">
    <rect x="0" y="0" width="1520" height="400" rx="20" fill="#e8eaf6" opacity="0.2"/>
    <text x="760" y="40" text-anchor="middle" font-family="Microsoft YaHei" font-size="36" font-weight="bold" fill="#3f51b5">讨论任务：</text>
    
    <!-- 任务1 -->
    <text x="50" y="100" font-family="Microsoft YaHei" font-size="32" font-weight="bold" fill="#3f51b5">1. 客户定位：</text>
    <text x="50" y="140" font-family="Microsoft YaHei" font-size="28" fill="#424242">请从你们小组负责的真实客户中，提名一个你们认为价值最高，</text>
    <text x="50" y="170" font-family="Microsoft YaHei" font-size="28" fill="#424242">但也最让你头疼的A类客户。</text>
    
    <circle cx="80" cy="210" r="6" fill="#3f51b5"/>
    <text x="100" y="220" font-family="Microsoft YaHei" font-size="26" fill="#424242">他处于"金字塔"的哪一层？</text>
    
    <circle cx="80" cy="250" r="6" fill="#3f51b5"/>
    <text x="100" y="260" font-family="Microsoft YaHei" font-size="26" fill="#424242">他目前的"健康灯号"是绿色、黄色还是红色？理由是？</text>
    
    <!-- 任务2 -->
    <text x="50" y="320" font-family="Microsoft YaHei" font-size="32" font-weight="bold" fill="#3f51b5">2. 行动抓手：</text>
    <text x="50" y="360" font-family="Microsoft YaHei" font-size="28" fill="#424242">针对这个客户，请用"连接+算力+能力"的框架，思考一下，</text>
    <text x="50" y="390" font-family="Microsoft YaHei" font-size="28" fill="#424242">除了现有业务，我们还有哪一个"新武器"，最有可能切入他的业务痛点，</text>
    <text x="50" y="420" font-family="Microsoft YaHei" font-size="28" fill="#424242">从而加深绑定？</text>
  </g>
  
  <!-- 倒计时器 -->
  <g transform="translate(1600,900)">
    <circle cx="0" cy="0" r="60" fill="#ff5722" opacity="0.8"/>
    <circle cx="0" cy="0" r="50" fill="#ffffff"/>
    <text x="0" y="-10" text-anchor="middle" font-family="Microsoft YaHei" font-size="24" font-weight="bold" fill="#ff5722">15</text>
    <text x="0" y="15" text-anchor="middle" font-family="Microsoft YaHei" font-size="18" fill="#ff5722">分钟</text>
    
    <!-- 时钟指针 -->
    <line x1="0" y1="0" x2="0" y2="-30" stroke="#ff5722" stroke-width="3"/>
    <line x1="0" y1="0" x2="20" y2="0" stroke="#ff5722" stroke-width="2"/>
    <circle cx="0" cy="0" r="3" fill="#ff5722"/>
  </g>
  
  <!-- 小组图标 -->
  <g transform="translate(300,200)">
    <circle cx="0" cy="0" r="25" fill="#4caf50" opacity="0.7"/>
    <circle cx="40" cy="0" r="25" fill="#2196f3" opacity="0.7"/>
    <circle cx="80" cy="0" r="25" fill="#ff9800" opacity="0.7"/>
    <circle cx="120" cy="0" r="25" fill="#9c27b0" opacity="0.7"/>
    
    <!-- 简化人形 -->
    <circle cx="0" cy="-8" r="8" fill="#ffffff"/>
    <rect x="-6" y="2" width="12" height="18" fill="#ffffff"/>
    
    <circle cx="40" cy="-8" r="8" fill="#ffffff"/>
    <rect x="34" y="2" width="12" height="18" fill="#ffffff"/>
    
    <circle cx="80" cy="-8" r="8" fill="#ffffff"/>
    <rect x="74" y="2" width="12" height="18" fill="#ffffff"/>
    
    <circle cx="120" cy="-8" r="8" fill="#ffffff"/>
    <rect x="114" y="2" width="12" height="18" fill="#ffffff"/>
  </g>
  
  <!-- 白板图标 -->
  <g transform="translate(1400,200)">
    <rect x="0" y="0" width="120" height="80" fill="#ffffff" stroke="#424242" stroke-width="3"/>
    <line x1="20" y1="20" x2="100" y2="20" stroke="#424242" stroke-width="2"/>
    <line x1="20" y1="35" x2="80" y2="35" stroke="#424242" stroke-width="2"/>
    <line x1="20" y1="50" x2="90" y2="50" stroke="#424242" stroke-width="2"/>
    <line x1="20" y1="65" x2="70" y2="65" stroke="#424242" stroke-width="2"/>
  </g>
  
  <!-- 底部装饰弧线 -->
  <path d="M0,950 Q960,900 1920,950" stroke="#e8eaf6" stroke-width="20" fill="none" opacity="0.4"/>
</svg>
