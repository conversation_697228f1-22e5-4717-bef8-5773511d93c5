<?xml version="1.0" encoding="UTF-8"?>
<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="#ffffff"/>
  
  <!-- 装饰性弧线背景 -->
  <path d="M0,100 Q960,50 1920,100" stroke="#fff3e0" stroke-width="25" fill="none" opacity="0.3"/>
  
  <!-- 标题 -->
  <text x="960" y="120" text-anchor="middle" font-family="Microsoft YaHei" font-size="48" font-weight="bold" fill="#f57c00">
    总结升华：处理价格异议的"黄金三法则"
  </text>
  
  <!-- 三把金钥匙 -->
  <g transform="translate(960,300)">
    <!-- 金钥匙1 -->
    <g transform="translate(-300,0)">
      <path d="M0,0 L30,0 L30,10 L40,10 L40,20 L30,20 L30,30 L0,30 Z" fill="#ffd700"/>
      <circle cx="-15" cy="15" r="15" fill="#ffd700"/>
      <circle cx="-15" cy="15" r="10" fill="#ffffff"/>
      <text x="-15" y="20" text-anchor="middle" font-family="Microsoft YaHei" font-size="12" font-weight="bold" fill="#f57c00">1</text>
    </g>
    
    <!-- 金钥匙2 -->
    <g transform="translate(0,0)">
      <path d="M0,0 L30,0 L30,10 L40,10 L40,20 L30,20 L30,30 L0,30 Z" fill="#ffd700"/>
      <circle cx="-15" cy="15" r="15" fill="#ffd700"/>
      <circle cx="-15" cy="15" r="10" fill="#ffffff"/>
      <text x="-15" y="20" text-anchor="middle" font-family="Microsoft YaHei" font-size="12" font-weight="bold" fill="#f57c00">2</text>
    </g>
    
    <!-- 金钥匙3 -->
    <g transform="translate(300,0)">
      <path d="M0,0 L30,0 L30,10 L40,10 L40,20 L30,20 L30,30 L0,30 Z" fill="#ffd700"/>
      <circle cx="-15" cy="15" r="15" fill="#ffd700"/>
      <circle cx="-15" cy="15" r="10" fill="#ffffff"/>
      <text x="-15" y="20" text-anchor="middle" font-family="Microsoft YaHei" font-size="12" font-weight="bold" fill="#f57c00">3</text>
    </g>
  </g>
  
  <!-- 法则一：价值锚定法 -->
  <g transform="translate(480,500)">
    <rect x="-400" y="-100" width="800" height="200" rx="20" fill="#e8f5e8" opacity="0.3"/>
    <text x="0" y="-60" text-anchor="middle" font-family="Microsoft YaHei" font-size="28" font-weight="bold" fill="#2e7d32">法则一：价值锚定法</text>
    <text x="0" y="-20" text-anchor="middle" font-family="Microsoft YaHei" font-size="22" fill="#424242">永远不要在价值讲透前，陷入价格的泥潭。</text>
    <text x="0" y="10" text-anchor="middle" font-family="Microsoft YaHei" font-size="22" font-weight="bold" fill="#2e7d32">先谈价值，再谈价格。</text>
  </g>
  
  <!-- 法则二：拆解对比法 -->
  <g transform="translate(1440,500)">
    <rect x="-400" y="-100" width="800" height="200" rx="20" fill="#e3f2fd" opacity="0.3"/>
    <text x="0" y="-60" text-anchor="middle" font-family="Microsoft YaHei" font-size="28" font-weight="bold" fill="#1565c0">法则二：拆解对比法</text>
    <text x="0" y="-20" text-anchor="middle" font-family="Microsoft YaHei" font-size="20" fill="#424242">将价格的"绝对值"，拆解成"相对值"（如/天/人）。</text>
    <text x="0" y="10" text-anchor="middle" font-family="Microsoft YaHei" font-size="20" fill="#424242">将一次性的"投入"，对比成持续的"收益"或避免的"损失"。</text>
  </g>
  
  <!-- 法则三：认知转移法 -->
  <g transform="translate(960,750)">
    <rect x="-600" y="-100" width="1200" height="200" rx="20" fill="#fff3e0" opacity="0.3"/>
    <text x="0" y="-60" text-anchor="middle" font-family="Microsoft YaHei" font-size="28" font-weight="bold" fill="#f57c00">法则三：认知转移法</text>
    <text x="0" y="-20" text-anchor="middle" font-family="Microsoft YaHei" font-size="22" fill="#424242">将客户的关注点，从"价格"，巧妙地转移到</text>
    <text x="0" y="10" text-anchor="middle" font-family="Microsoft YaHei" font-size="22" fill="#424242">"风险"、"机会成本"、"服务保障"等我们更有优势的维度上去。</text>
  </g>
  
  <!-- 装饰性宝箱图标 -->
  <g transform="translate(200,400)" opacity="0.4">
    <rect x="0" y="0" width="40" height="25" rx="5" fill="#8d6e63"/>
    <rect x="5" y="5" width="30" height="15" fill="#ffd700"/>
    <circle cx="20" cy="12" r="3" fill="#f57c00"/>
    <rect x="18" y="-5" width="4" height="10" fill="#8d6e63"/>
  </g>
  
  <g transform="translate(1720,400)" opacity="0.4">
    <rect x="-40" y="0" width="40" height="25" rx="5" fill="#8d6e63"/>
    <rect x="-35" y="5" width="30" height="15" fill="#ffd700"/>
    <circle cx="-20" cy="12" r="3" fill="#f57c00"/>
    <rect x="-22" y="-5" width="4" height="10" fill="#8d6e63"/>
  </g>
</svg>
