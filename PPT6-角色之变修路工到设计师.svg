<?xml version="1.0" encoding="UTF-8"?>
<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="#ffffff"/>
  
  <!-- 装饰性弧线背景 -->
  <path d="M0,100 Q960,50 1920,100" stroke="#e8f5e8" stroke-width="25" fill="none" opacity="0.3"/>
  
  <!-- 标题 -->
  <text x="960" y="120" text-anchor="middle" font-family="Microsoft YaHei" font-size="56" font-weight="bold" fill="#2e7d32">
    角色之变：从"修路工"到"智慧交通设计师"
  </text>
  
  <!-- 左侧：过去 -->
  <g transform="translate(200,200)">
    <!-- 高速公路图片区域 -->
    <rect x="0" y="0" width="600" height="300" rx="15" fill="#ffebee" stroke="#f44336" stroke-width="3"/>
    <text x="300" y="30" text-anchor="middle" font-family="Microsoft YaHei" font-size="24" font-weight="bold" fill="#d32f2f">管道化风险</text>
    
    <!-- 高速公路简化图 -->
    <rect x="50" y="80" width="500" height="60" fill="#757575" opacity="0.6"/>
    <rect x="50" y="105" width="500" height="10" fill="#ffeb3b" opacity="0.8"/>
    <rect x="50" y="125" width="500" height="10" fill="#ffeb3b" opacity="0.8"/>
    
    <!-- 车辆 -->
    <rect x="150" y="90" width="40" height="20" rx="5" fill="#2196f3"/>
    <rect x="250" y="90" width="40" height="20" rx="5" fill="#4caf50"/>
    <rect x="350" y="90" width="40" height="20" rx="5" fill="#ff9800"/>
    <rect x="450" y="90" width="40" height="20" rx="5" fill="#9c27b0"/>
    
    <text x="300" y="180" text-anchor="middle" font-family="Microsoft YaHei" font-size="36" font-weight="bold" fill="#2e7d32">过去：信息高速公路的"修路工"</text>
    
    <!-- 特点列表 -->
    <circle cx="50" cy="220" r="6" fill="#4caf50"/>
    <text x="70" y="230" font-family="Microsoft YaHei" font-size="28" fill="#424242">提供稳定连接</text>
    
    <circle cx="50" cy="260" r="6" fill="#4caf50"/>
    <text x="70" y="270" font-family="Microsoft YaHei" font-size="28" fill="#424242">收取"过路费"</text>
    
    <circle cx="50" cy="300" r="6" fill="#4caf50"/>
    <text x="70" y="310" font-family="Microsoft YaHei" font-size="28" fill="#424242">价值被上层应用攫取</text>
  </g>
  
  <!-- 中间箭头 -->
  <g transform="translate(960,450)">
    <path d="M-100,0 L100,0" stroke="#2e7d32" stroke-width="8" marker-end="url(#bigArrow)"/>
    <circle cx="0" cy="0" r="40" fill="#4caf50"/>
    <text x="0" y="8" text-anchor="middle" font-family="Microsoft YaHei" font-size="24" font-weight="bold" fill="#ffffff">进化</text>
  </g>
  
  <!-- 右侧：现在 -->
  <g transform="translate(1120,200)">
    <!-- 智慧城市图片区域 -->
    <rect x="0" y="0" width="600" height="300" rx="15" fill="#e8f5e8" stroke="#4caf50" stroke-width="3"/>
    <text x="300" y="30" text-anchor="middle" font-family="Microsoft YaHei" font-size="24" font-weight="bold" fill="#2e7d32">生态化机遇</text>
    
    <!-- 智慧交通大屏简化图 -->
    <rect x="50" y="60" width="500" height="180" fill="#263238" opacity="0.8"/>
    <rect x="70" y="80" width="460" height="140" fill="#37474f"/>
    
    <!-- 大屏内容 -->
    <rect x="90" y="100" width="100" height="60" fill="#4caf50" opacity="0.7"/>
    <rect x="210" y="100" width="100" height="60" fill="#2196f3" opacity="0.7"/>
    <rect x="330" y="100" width="100" height="60" fill="#ff9800" opacity="0.7"/>
    <rect x="450" y="100" width="60" height="60" fill="#f44336" opacity="0.7"/>
    
    <rect x="90" y="180" width="200" height="30" fill="#9c27b0" opacity="0.7"/>
    <rect x="310" y="180" width="200" height="30" fill="#607d8b" opacity="0.7"/>
    
    <text x="300" y="270" text-anchor="middle" font-family="Microsoft YaHei" font-size="36" font-weight="bold" fill="#2e7d32">现在：数智服务生态的"总设计师"</text>
    
    <!-- 特点列表 -->
    <circle cx="50" cy="320" r="6" fill="#4caf50"/>
    <text x="70" y="330" font-family="Microsoft YaHei" font-size="28" fill="#424242">资源整合者</text>
    
    <circle cx="50" cy="360" r="6" fill="#4caf50"/>
    <text x="70" y="370" font-family="Microsoft YaHei" font-size="28" fill="#424242">能力赋能者</text>
    
    <circle cx="50" cy="400" r="6" fill="#4caf50"/>
    <text x="70" y="410" font-family="Microsoft YaHei" font-size="28" fill="#424242">生态构建者</text>
  </g>
  
  <!-- 箭头标记定义 -->
  <defs>
    <marker id="bigArrow" markerWidth="20" markerHeight="14" refX="18" refY="7" orient="auto">
      <polygon points="0 0, 20 7, 0 14" fill="#2e7d32"/>
    </marker>
  </defs>
  
  <!-- 底部装饰弧线 -->
  <path d="M0,950 Q960,900 1920,950" stroke="#e8f5e8" stroke-width="20" fill="none" opacity="0.4"/>
</svg>
