<?xml version="1.0" encoding="UTF-8"?>
<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景渐变 -->
  <defs>
    <linearGradient id="chessGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#3e2723;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#5d4037;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#8d6e63;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="url(#chessGradient)"/>
  
  <!-- 复杂棋局背景 -->
  <g transform="translate(960,400)">
    <!-- 棋盘 -->
    <rect x="-200" y="-150" width="400" height="300" fill="#8d6e63" opacity="0.8"/>
    <rect x="-180" y="-130" width="360" height="260" fill="#ffffff" opacity="0.9"/>
    
    <!-- 棋盘网格 -->
    <g stroke="#8d6e63" stroke-width="2" opacity="0.6">
      <line x1="-150" y1="-100" x2="150" y2="-100"/>
      <line x1="-150" y1="-50" x2="150" y2="-50"/>
      <line x1="-150" y1="0" x2="150" y2="0"/>
      <line x1="-150" y1="50" x2="150" y2="50"/>
      <line x1="-150" y1="100" x2="150" y2="100"/>
      
      <line x1="-100" y1="-130" x2="-100" y2="130"/>
      <line x1="-50" y1="-130" x2="-50" y2="130"/>
      <line x1="0" y1="-130" x2="0" y2="130"/>
      <line x1="50" y1="-130" x2="50" y2="130"/>
      <line x1="100" y1="-130" x2="100" y2="130"/>
    </g>
    
    <!-- 黑白棋子 -->
    <circle cx="-75" cy="-75" r="15" fill="#37474f"/>
    <circle cx="25" cy="-75" r="15" fill="#ffffff" stroke="#37474f" stroke-width="2"/>
    <circle cx="-25" cy="-25" r="15" fill="#37474f"/>
    <circle cx="75" cy="-25" r="15" fill="#ffffff" stroke="#37474f" stroke-width="2"/>
    <circle cx="-75" cy="25" r="15" fill="#ffffff" stroke="#37474f" stroke-width="2"/>
    <circle cx="25" cy="25" r="15" fill="#37474f"/>
    <circle cx="75" cy="75" r="15" fill="#37474f"/>
    <circle cx="-25" cy="75" r="15" fill="#ffffff" stroke="#37474f" stroke-width="2"/>
    
    <!-- 博弈思考线 -->
    <g stroke="#ffeb3b" stroke-width="2" fill="none" opacity="0.6">
      <path d="M-75,-75 Q0,-50 75,75"/>
      <path d="M25,-75 Q-50,0 -25,75"/>
    </g>
    
    <!-- 智慧光芒 -->
    <g opacity="0.7">
      <circle cx="-100" cy="-100" r="3" fill="#ffeb3b"/>
      <circle cx="100" cy="-100" r="3" fill="#ffeb3b"/>
      <circle cx="-100" cy="100" r="3" fill="#ffeb3b"/>
      <circle cx="100" cy="100" r="3" fill="#ffeb3b"/>
    </g>
  </g>
  
  <!-- 模块标题 -->
  <text x="960" y="200" text-anchor="middle" font-family="Microsoft YaHei" font-size="72" font-weight="bold" fill="#ffffff">
    晚间专题复盘会
  </text>
  
  <!-- 副标题 -->
  <text x="960" y="750" text-anchor="middle" font-family="Microsoft YaHei" font-size="48" fill="#ffffff" opacity="0.9">
    ——异议处理攻坚战
  </text>
  
  <!-- 装饰性元素 -->
  <g opacity="0.4">
    <circle cx="200" cy="200" r="6" fill="#ffeb3b"/>
    <circle cx="1720" cy="200" r="6" fill="#ffeb3b"/>
    <circle cx="200" cy="880" r="6" fill="#ffeb3b"/>
    <circle cx="1720" cy="880" r="6" fill="#ffeb3b"/>
  </g>
  
  <!-- 智慧光环 -->
  <circle cx="960" cy="400" r="450" fill="none" stroke="#ffeb3b" stroke-width="3" opacity="0.5"/>
  <circle cx="960" cy="400" r="480" fill="none" stroke="#ffffff" stroke-width="1" opacity="0.4"/>
  
  <!-- 思考波纹效果 -->
  <g opacity="0.6">
    <path d="M400,300 Q420,280 440,300 Q460,280 480,300" stroke="#ffeb3b" stroke-width="3" fill="none"/>
    <path d="M1440,300 Q1460,280 1480,300 Q1500,280 1520,300" stroke="#ffeb3b" stroke-width="3" fill="none"/>
    <path d="M400,780 Q420,800 440,780 Q460,800 480,780" stroke="#ffeb3b" stroke-width="3" fill="none"/>
    <path d="M1440,780 Q1460,800 1480,780 Q1500,800 1520,780" stroke="#ffeb3b" stroke-width="3" fill="none"/>
  </g>
</svg>
