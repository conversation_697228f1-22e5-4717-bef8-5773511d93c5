<?xml version="1.0" encoding="UTF-8"?>
<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="#ffffff"/>
  
  <!-- 装饰性弧线背景 -->
  <path d="M0,100 Q960,50 1920,100" stroke="#e8f5e8" stroke-width="25" fill="none" opacity="0.3"/>
  
  <!-- 标题 -->
  <text x="960" y="120" text-anchor="middle" font-family="Microsoft YaHei" font-size="56" font-weight="bold" fill="#2e7d32">
    本节总结：三大认知破局
  </text>
  
  <!-- 战场观破局 -->
  <g transform="translate(320,250)">
    <rect x="0" y="0" width="500" height="200" rx="15" fill="#e8f5e8" opacity="0.3"/>
    <text x="250" y="40" text-anchor="middle" font-family="Microsoft YaHei" font-size="36" font-weight="bold" fill="#2e7d32">战场观破局</text>
    
    <!-- 破碎到完整的图标演变 -->
    <g transform="translate(100,80)">
      <!-- 破碎状态 -->
      <g opacity="0.4">
        <rect x="-20" y="-10" width="15" height="15" fill="#757575" transform="rotate(15)"/>
        <rect x="0" y="-5" width="12" height="12" fill="#757575" transform="rotate(-10)"/>
        <rect x="15" y="5" width="10" height="10" fill="#757575" transform="rotate(25)"/>
      </g>
      
      <!-- 箭头 -->
      <path d="M40,0 L80,0" stroke="#4caf50" stroke-width="3" marker-end="url(#greenArrow)"/>
      
      <!-- 完整状态 -->
      <circle cx="120" cy="0" r="20" fill="#4caf50" opacity="0.8"/>
      <text x="120" y="5" text-anchor="middle" font-family="Microsoft YaHei" font-size="16" fill="#ffffff">生态</text>
    </g>
    
    <text x="50" y="130" font-family="Microsoft YaHei" font-size="24" fill="#424242">从"卖产品"的单点思维</text>
    <text x="50" y="160" font-family="Microsoft YaHei" font-size="24" fill="#424242">到"做生态"的全局思维</text>
  </g>
  
  <!-- 客户观破局 -->
  <g transform="translate(960,250)">
    <rect x="0" y="0" width="500" height="200" rx="15" fill="#e3f2fd" opacity="0.3"/>
    <text x="250" y="40" text-anchor="middle" font-family="Microsoft YaHei" font-size="36" font-weight="bold" fill="#1565c0">客户观破局</text>
    
    <!-- 破碎到完整的图标演变 -->
    <g transform="translate(100,80)">
      <!-- 破碎状态 -->
      <g opacity="0.4">
        <rect x="-15" y="-8" width="12" height="8" fill="#757575"/>
        <rect x="5" y="-5" width="10" height="6" fill="#757575"/>
        <rect x="-5" y="5" width="8" height="5" fill="#757575"/>
      </g>
      
      <!-- 箭头 -->
      <path d="M40,0 L80,0" stroke="#2196f3" stroke-width="3" marker-end="url(#blueArrow)"/>
      
      <!-- 完整状态 -->
      <ellipse cx="120" cy="0" rx="25" ry="15" fill="#2196f3" opacity="0.8"/>
      <text x="120" y="5" text-anchor="middle" font-family="Microsoft YaHei" font-size="14" fill="#ffffff">LTV</text>
    </g>
    
    <text x="50" y="130" font-family="Microsoft YaHei" font-size="24" fill="#424242">从"看单点ARPU"的交易思维</text>
    <text x="50" y="160" font-family="Microsoft YaHei" font-size="24" fill="#424242">到"看周期LTV"的经营思维</text>
  </g>
  
  <!-- 保有观破局 -->
  <g transform="translate(640,500)">
    <rect x="0" y="0" width="500" height="200" rx="15" fill="#fff3e0" opacity="0.3"/>
    <text x="250" y="40" text-anchor="middle" font-family="Microsoft YaHei" font-size="36" font-weight="bold" fill="#f57c00">保有观破局</text>
    
    <!-- 破碎到完整的图标演变 -->
    <g transform="translate(100,80)">
      <!-- 破碎状态 -->
      <g opacity="0.4">
        <path d="M-15,-5 L-10,5 L-5,-5" stroke="#757575" stroke-width="3" fill="none"/>
        <path d="M0,5 L5,-5 L10,5" stroke="#757575" stroke-width="3" fill="none"/>
        <circle cx="15" cy="0" r="3" fill="#757575"/>
      </g>
      
      <!-- 箭头 -->
      <path d="M40,0 L80,0" stroke="#ff9800" stroke-width="3" marker-end="url(#orangeArrow)"/>
      
      <!-- 完整状态 -->
      <circle cx="120" cy="0" r="18" fill="#ff9800" opacity="0.8"/>
      <path d="M110,-5 L115,0 L125,-10" stroke="#ffffff" stroke-width="2" fill="none"/>
      <circle cx="120" cy="5" r="3" fill="#ffffff"/>
    </g>
    
    <text x="50" y="130" font-family="Microsoft YaHei" font-size="24" fill="#424242">从"被动救火"的消防员思维</text>
    <text x="50" y="160" font-family="Microsoft YaHei" font-size="24" fill="#424242">到"主动保健"的医生思维</text>
  </g>
  
  <!-- 底部总结 -->
  <g transform="translate(960,800)">
    <rect x="-400" y="-40" width="800" height="80" rx="20" fill="#4caf50" opacity="0.1"/>
    <text x="0" y="-10" text-anchor="middle" font-family="Microsoft YaHei" font-size="36" font-weight="bold" fill="#2e7d32">
      思想的地基打好了，才能在上面构建起高效的拓新大厦！
    </text>
  </g>
  
  <!-- 连接线 -->
  <path d="M570,350 Q750,400 890,350" stroke="#4caf50" stroke-width="3" fill="none" opacity="0.4"/>
  <path d="M1210,450 Q1100,500 890,500" stroke="#2196f3" stroke-width="3" fill="none" opacity="0.4"/>
  <path d="M890,600 Q750,650 570,600" stroke="#ff9800" stroke-width="3" fill="none" opacity="0.4"/>
  
  <!-- 箭头标记定义 -->
  <defs>
    <marker id="greenArrow" markerWidth="8" markerHeight="6" refX="7" refY="3" orient="auto">
      <polygon points="0 0, 8 3, 0 6" fill="#4caf50"/>
    </marker>
    <marker id="blueArrow" markerWidth="8" markerHeight="6" refX="7" refY="3" orient="auto">
      <polygon points="0 0, 8 3, 0 6" fill="#2196f3"/>
    </marker>
    <marker id="orangeArrow" markerWidth="8" markerHeight="6" refX="7" refY="3" orient="auto">
      <polygon points="0 0, 8 3, 0 6" fill="#ff9800"/>
    </marker>
  </defs>
</svg>
