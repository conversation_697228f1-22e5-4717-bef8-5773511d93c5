<?xml version="1.0" encoding="UTF-8"?>
<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="#ffffff"/>
  
  <!-- 装饰性弧线背景 -->
  <path d="M0,100 Q960,50 1920,100" stroke="#e8eaf6" stroke-width="25" fill="none" opacity="0.3"/>
  
  <!-- 标题 -->
  <text x="960" y="120" text-anchor="middle" font-family="Microsoft YaHei" font-size="56" font-weight="bold" fill="#3f51b5">
    作战地图的核心四要素（四张图）
  </text>
  
  <!-- 象限一：人脉图 -->
  <g transform="translate(480,300)">
    <rect x="0" y="0" width="400" height="300" rx="15" fill="#e8f5e8" opacity="0.3"/>
    <text x="200" y="30" text-anchor="middle" font-family="Microsoft YaHei" font-size="24" font-weight="bold" fill="#2e7d32">象限一：人脉图</text>
    <text x="200" y="55" text-anchor="middle" font-family="Microsoft YaHei" font-size="20" fill="#2e7d32">(组织架构与关键人)</text>

    <text x="200" y="90" text-anchor="middle" font-family="Microsoft YaHei" font-size="20" font-weight="bold" fill="#2e7d32">核心问题：我们应该和谁对话？</text>
    <text x="200" y="115" text-anchor="middle" font-family="Microsoft YaHei" font-size="20" fill="#424242">谁是朋友？谁是敌人？</text>

    <!-- 人物关系网络图标 -->
    <g transform="translate(200,180)">
      <circle cx="0" cy="0" r="15" fill="#4caf50"/>
      <circle cx="-40" cy="-30" r="12" fill="#2196f3"/>
      <circle cx="40" cy="-30" r="12" fill="#2196f3"/>
      <circle cx="-60" cy="20" r="10" fill="#ff9800"/>
      <circle cx="0" cy="40" r="10" fill="#ff9800"/>
      <circle cx="60" cy="20" r="10" fill="#ff9800"/>

      <!-- 连接线 -->
      <line x1="0" y1="0" x2="-40" y2="-30" stroke="#424242" stroke-width="2"/>
      <line x1="0" y1="0" x2="40" y2="-30" stroke="#424242" stroke-width="2"/>
      <line x1="0" y1="0" x2="-60" y2="20" stroke="#424242" stroke-width="2"/>
      <line x1="0" y1="0" x2="0" y2="40" stroke="#424242" stroke-width="2"/>
      <line x1="0" y1="0" x2="60" y2="20" stroke="#424242" stroke-width="2"/>
    </g>
  </g>

  <!-- 象限二：流程图 -->
  <g transform="translate(1040,300)">
    <rect x="0" y="0" width="400" height="300" rx="15" fill="#e3f2fd" opacity="0.3"/>
    <text x="200" y="30" text-anchor="middle" font-family="Microsoft YaHei" font-size="24" font-weight="bold" fill="#1565c0">象限二：流程图</text>
    <text x="200" y="55" text-anchor="middle" font-family="Microsoft YaHei" font-size="20" fill="#1565c0">(关键决策链分析)</text>

    <text x="200" y="90" text-anchor="middle" font-family="Microsoft YaHei" font-size="20" font-weight="bold" fill="#1565c0">核心问题：客户内部是如何做决策的？</text>
    <text x="200" y="115" text-anchor="middle" font-family="Microsoft YaHei" font-size="20" fill="#424242">我们现在走到了哪一步？</text>

    <!-- 多步骤流程图标 -->
    <g transform="translate(200,180)">
      <rect x="-80" y="-20" width="40" height="20" rx="5" fill="#2196f3"/>
      <rect x="-20" y="-20" width="40" height="20" rx="5" fill="#2196f3"/>
      <rect x="40" y="-20" width="40" height="20" rx="5" fill="#ff9800"/>

      <!-- 箭头 -->
      <path d="M-40,-10 L-30,-10" stroke="#424242" stroke-width="2" marker-end="url(#flowArrow)"/>
      <path d="M20,-10 L30,-10" stroke="#424242" stroke-width="2" marker-end="url(#flowArrow)"/>

      <text x="-60" y="-5" text-anchor="middle" font-family="Microsoft YaHei" font-size="10" fill="#ffffff">需求</text>
      <text x="0" y="-5" text-anchor="middle" font-family="Microsoft YaHei" font-size="10" fill="#ffffff">评估</text>
      <text x="60" y="-5" text-anchor="middle" font-family="Microsoft YaHei" font-size="10" fill="#ffffff">决策</text>
    </g>
  </g>
  
  <!-- 象限三：需求图 -->
  <g transform="translate(480,650)">
    <rect x="0" y="0" width="400" height="300" rx="15" fill="#fff3e0" opacity="0.3"/>
    <text x="200" y="30" text-anchor="middle" font-family="Microsoft YaHei" font-size="24" font-weight="bold" fill="#f57c00">象限三：需求图</text>
    <text x="200" y="55" text-anchor="middle" font-family="Microsoft YaHei" font-size="20" fill="#f57c00">(业务需求与痛点)</text>

    <text x="200" y="90" text-anchor="middle" font-family="Microsoft YaHei" font-size="20" font-weight="bold" fill="#f57c00">核心问题：客户的"痛"在哪里？</text>
    <text x="200" y="115" text-anchor="middle" font-family="Microsoft YaHei" font-size="20" fill="#424242">我们的"药"应该用在哪里？</text>

    <!-- 冰山图标 -->
    <g transform="translate(200,180)">
      <!-- 海平面 -->
      <line x1="-60" y1="0" x2="60" y2="0" stroke="#03a9f4" stroke-width="3"/>
      <!-- 冰山上部分 -->
      <polygon points="0,-30 -30,0 30,0" fill="#b3e5fc"/>
      <!-- 冰山下部分 -->
      <polygon points="-30,0 -50,40 50,40 30,0" fill="#4fc3f7" opacity="0.8"/>

      <text x="0" y="-15" text-anchor="middle" font-family="Microsoft YaHei" font-size="10" fill="#0277bd">显性需求</text>
      <text x="0" y="25" text-anchor="middle" font-family="Microsoft YaHei" font-size="10" fill="#ffffff">隐性需求</text>
    </g>
  </g>

  <!-- 象限四：阵地图 -->
  <g transform="translate(1040,650)">
    <rect x="0" y="0" width="400" height="300" rx="15" fill="#fce4ec" opacity="0.3"/>
    <text x="200" y="30" text-anchor="middle" font-family="Microsoft YaHei" font-size="24" font-weight="bold" fill="#c2185b">象限四：阵地图</text>
    <text x="200" y="55" text-anchor="middle" font-family="Microsoft YaHei" font-size="20" fill="#c2185b">(成员分布与竞对)</text>

    <text x="200" y="90" text-anchor="middle" font-family="Microsoft YaHei" font-size="20" font-weight="bold" fill="#c2185b">核心问题：我们的阵地在哪？</text>
    <text x="200" y="115" text-anchor="middle" font-family="Microsoft YaHei" font-size="20" fill="#424242">敌人的阵地在哪？主攻方向是？</text>

    <!-- 棋盘图标 -->
    <g transform="translate(200,180)">
      <rect x="-40" y="-40" width="80" height="80" fill="#8d6e63" opacity="0.8"/>
      <!-- 棋盘线 -->
      <line x1="-30" y1="-30" x2="30" y2="-30" stroke="#5d4037" stroke-width="1"/>
      <line x1="-30" y1="-10" x2="30" y2="-10" stroke="#5d4037" stroke-width="1"/>
      <line x1="-30" y1="10" x2="30" y2="10" stroke="#5d4037" stroke-width="1"/>
      <line x1="-30" y1="30" x2="30" y2="30" stroke="#5d4037" stroke-width="1"/>
      <line x1="-30" y1="-30" x2="-30" y2="30" stroke="#5d4037" stroke-width="1"/>
      <line x1="-10" y1="-30" x2="-10" y2="30" stroke="#5d4037" stroke-width="1"/>
      <line x1="10" y1="-30" x2="10" y2="30" stroke="#5d4037" stroke-width="1"/>
      <line x1="30" y1="-30" x2="30" y2="30" stroke="#5d4037" stroke-width="1"/>

      <!-- 棋子 -->
      <circle cx="-20" cy="-20" r="5" fill="#4caf50"/>
      <circle cx="0" cy="0" r="5" fill="#f44336"/>
      <circle cx="20" cy="20" r="5" fill="#4caf50"/>
      <circle cx="-10" cy="10" r="5" fill="#f44336"/>
    </g>
  </g>
  
  <!-- 箭头标记定义 -->
  <defs>
    <marker id="flowArrow" markerWidth="8" markerHeight="6" refX="7" refY="3" orient="auto">
      <polygon points="0 0, 8 3, 0 6" fill="#424242"/>
    </marker>
  </defs>
</svg>
