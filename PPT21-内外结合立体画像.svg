<?xml version="1.0" encoding="UTF-8"?>
<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="#ffffff"/>
  
  <!-- 装饰性弧线背景 -->
  <path d="M0,100 Q960,50 1920,100" stroke="#f3e5f5" stroke-width="25" fill="none" opacity="0.3"/>
  
  <!-- 标题 -->
  <text x="960" y="120" text-anchor="middle" font-family="Microsoft YaHei" font-size="56" font-weight="bold" fill="#7b1fa2">
    内外结合：让画像从"清晰"走向"立体"
  </text>
  
  <!-- 公式展示 -->
  <g transform="translate(960,350)">
    <!-- 内部数据 -->
    <g transform="translate(-300,0)">
      <rect x="-100" y="-50" width="200" height="100" rx="15" fill="#e3f2fd" opacity="0.3"/>
      <text x="0" y="-10" text-anchor="middle" font-family="Microsoft YaHei" font-size="28" font-weight="bold" fill="#1565c0">内部数据</text>
      <text x="0" y="20" text-anchor="middle" font-family="Microsoft YaHei" font-size="22" fill="#424242">(他做了什么)</text>
      
      <!-- 数据报表图标 -->
      <g transform="translate(0,-80)">
        <rect x="-20" y="-15" width="40" height="30" fill="#2196f3" opacity="0.8"/>
        <rect x="-15" y="-10" width="30" height="20" fill="#ffffff"/>
        <line x1="-10" y1="-5" x2="10" y2="-5" stroke="#2196f3" stroke-width="2"/>
        <line x1="-10" y1="0" x2="5" y2="0" stroke="#2196f3" stroke-width="2"/>
        <line x1="-10" y1="5" x2="8" y2="5" stroke="#2196f3" stroke-width="2"/>
      </g>
    </g>
    
    <!-- 加号 -->
    <text x="0" y="10" text-anchor="middle" font-family="Microsoft YaHei" font-size="48" font-weight="bold" fill="#7b1fa2">+</text>
    
    <!-- 外部数据 -->
    <g transform="translate(300,0)">
      <rect x="-100" y="-50" width="200" height="100" rx="15" fill="#e8f5e8" opacity="0.3"/>
      <text x="0" y="-10" text-anchor="middle" font-family="Microsoft YaHei" font-size="28" font-weight="bold" fill="#2e7d32">外部数据</text>
      <text x="0" y="20" text-anchor="middle" font-family="Microsoft YaHei" font-size="22" fill="#424242">(他身处何处)</text>
      
      <!-- 地球和放大镜图标 -->
      <g transform="translate(0,-80)">
        <circle cx="0" cy="0" r="15" fill="#4caf50" opacity="0.8"/>
        <circle cx="0" cy="0" r="10" fill="#ffffff"/>
        <path d="M-5,-5 Q0,-10 5,-5" stroke="#4caf50" stroke-width="2" fill="none"/>
        <path d="M-5,5 Q0,0 5,5" stroke="#4caf50" stroke-width="2" fill="none"/>
        <circle cx="12" cy="12" r="8" fill="none" stroke="#ff9800" stroke-width="3"/>
        <line x1="18" y1="18" x2="25" y2="25" stroke="#ff9800" stroke-width="3"/>
      </g>
    </g>
    
    <!-- 等号 -->
    <text x="0" y="150" text-anchor="middle" font-family="Microsoft YaHei" font-size="48" font-weight="bold" fill="#7b1fa2">=</text>
    
    <!-- 立体用户画像 -->
    <g transform="translate(0,250)">
      <rect x="-150" y="-50" width="300" height="100" rx="15" fill="#f3e5f5" opacity="0.3"/>
      <text x="0" y="-10" text-anchor="middle" font-family="Microsoft YaHei" font-size="28" font-weight="bold" fill="#7b1fa2">立体用户画像</text>
      <text x="0" y="20" text-anchor="middle" font-family="Microsoft YaHei" font-size="22" fill="#424242">(他是一个怎样的人)</text>
      
      <!-- 立体人物模型图标 -->
      <g transform="translate(0,-80)">
        <ellipse cx="0" cy="10" rx="20" ry="8" fill="#9c27b0" opacity="0.3"/>
        <circle cx="0" cy="-10" r="12" fill="#9c27b0" opacity="0.8"/>
        <rect x="-8" y="2" width="16" height="20" fill="#9c27b0" opacity="0.8"/>
        <circle cx="0" cy="-10" r="8" fill="#ffffff"/>
        <circle cx="-3" cy="-12" r="2" fill="#9c27b0"/>
        <circle cx="3" cy="-12" r="2" fill="#9c27b0"/>
        <path d="M-3,-6 Q0,-4 3,-6" stroke="#9c27b0" stroke-width="2" fill="none"/>
      </g>
    </g>
  </g>
  
  <!-- 外部数据来源 -->
  <g transform="translate(960,700)">
    <text x="0" y="0" text-anchor="middle" font-family="Microsoft YaHei" font-size="32" font-weight="bold" fill="#424242">外部数据三大来源：</text>
    
    <circle cx="-400" cy="50" r="8" fill="#4caf50"/>
    <text x="-380" y="60" font-family="Microsoft YaHei" font-size="26" fill="#424242">1. 企业公开信息：企查查、官网、行业新闻 (判断企业发展阶段与战略)</text>
    
    <circle cx="-400" cy="90" r="8" fill="#2196f3"/>
    <text x="-380" y="100" font-family="Microsoft YaHei" font-size="26" fill="#424242">2. 地理位置信息 (LBS)：职住分析、活动热区 (赋能社区营销与异业合作)</text>
    
    <circle cx="-400" cy="130" r="8" fill="#ff9800"/>
    <text x="-380" y="140" font-family="Microsoft YaHei" font-size="26" fill="#424242">3. 客户方合作获取：HR/行政部门提供的群体性统计数据 (最精准的情报)</text>
  </g>
</svg>
