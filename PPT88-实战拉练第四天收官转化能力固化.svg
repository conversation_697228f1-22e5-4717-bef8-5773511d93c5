<?xml version="1.0" encoding="UTF-8"?>
<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景渐变 -->
  <defs>
    <linearGradient id="summitGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#e65100;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#ff9800;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#ffb74d;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="url(#summitGradient)"/>
  
  <!-- 登山运动员触摸顶峰 -->
  <g transform="translate(960,400)">
    <!-- 山峰轮廓 -->
    <path d="M-400,200 L-200,0 L-100,50 L0,-100 L100,30 L200,-50 L400,200 Z" fill="#8d6e63" opacity="0.8"/>
    <path d="M-380,180 L-180,20 L-80,70 L20,-80 L120,50 L220,-30 L380,180 Z" fill="#a1887f"/>
    
    <!-- 雪峰 -->
    <path d="M-50,-100 L0,-120 L50,-100 L30,-90 L0,-100 L-30,-90 Z" fill="#ffffff" opacity="0.9"/>
    
    <!-- 登山运动员 -->
    <g transform="translate(-20,-80)">
      <circle cx="0" cy="-15" r="12" fill="#2196f3" opacity="0.8"/>
      <rect x="-10" y="0" width="20" height="30" fill="#2196f3" opacity="0.8"/>
      
      <!-- 伸出的手臂 -->
      <rect x="10" y="-10" width="25" height="8" fill="#2196f3" opacity="0.8"/>
      <circle cx="35" cy="-6" r="6" fill="#ffdbcb"/>
      
      <!-- 登山装备 -->
      <rect x="-15" y="5" width="30" height="20" fill="#1565c0"/>
      <rect x="5" y="30" width="8" height="25" fill="#2196f3" opacity="0.8"/>
      <rect x="-13" y="30" width="8" height="25" fill="#2196f3" opacity="0.8"/>
    </g>
    
    <!-- 日出效果 -->
    <g transform="translate(300,-150)" opacity="0.8">
      <circle cx="0" cy="0" r="60" fill="#ffeb3b" opacity="0.9"/>
      <circle cx="0" cy="0" r="40" fill="#ffc107"/>
      <circle cx="0" cy="0" r="20" fill="#ff9800"/>
      
      <!-- 阳光 -->
      <g stroke="#ffeb3b" stroke-width="4" opacity="0.8">
        <line x1="0" y1="-80" x2="0" y2="-100"/>
        <line x1="57" y1="-57" x2="71" y2="-71"/>
        <line x1="80" y1="0" x2="100" y2="0"/>
        <line x1="57" y1="57" x2="71" y2="71"/>
        <line x1="0" y1="80" x2="0" y2="100"/>
        <line x1="-57" y1="57" x2="-71" y2="71"/>
        <line x1="-80" y1="0" x2="-100" y2="0"/>
        <line x1="-57" y1="-57" x2="-71" y2="-71"/>
      </g>
    </g>
    
    <!-- 希望与力量的光芒 */
    <g opacity="0.7">
      <path d="M-300,-50 L-280,-30 L-300,-10" stroke="#ffffff" stroke-width="3" fill="none"/>
      <path d="M300,-50 L280,-30 L300,-10" stroke="#ffffff" stroke-width="3" fill="none"/>
      <circle cx="-250" cy="-80" r="3" fill="#ffffff"/>
      <circle cx="250" cy="-80" r="3" fill="#ffffff"/>
      <circle cx="-280" cy="50" r="3" fill="#ffffff"/>
      <circle cx="280" cy="50" r="3" fill="#ffffff"/>
    </g>
  </g>
  
  <!-- 模块标题 -->
  <text x="960" y="200" text-anchor="middle" font-family="Microsoft YaHei" font-size="48" font-weight="bold" fill="#ffffff">
    实战拉练 · 第四天
  </text>
  <text x="960" y="280" text-anchor="middle" font-family="Microsoft YaHei" font-size="72" font-weight="bold" fill="#ffffff">
    收官转化 · 能力固化
  </text>
  
  <!-- 副标题 -->
  <text x="960" y="750" text-anchor="middle" font-family="Microsoft YaHei" font-size="42" fill="#ffffff" opacity="0.9">
    ——最后的冲锋，荣耀的加冕
  </text>
  
  <!-- 装饰性元素 -->
  <g opacity="0.5">
    <circle cx="200" cy="200" r="6" fill="#ffffff"/>
    <circle cx="1720" cy="200" r="6" fill="#ffffff"/>
    <circle cx="200" cy="880" r="6" fill="#ffffff"/>
    <circle cx="1720" cy="880" r="6" fill="#ffffff"/>
  </g>
  
  <!-- 荣耀光环 */
  <circle cx="960" cy="400" r="450" fill="none" stroke="#ffffff" stroke-width="3" opacity="0.4"/>
  <circle cx="960" cy="400" r="480" fill="none" stroke="#ffeb3b" stroke-width="1" opacity="0.5"/>
  
  <!-- 胜利的波纹 */
  <g opacity="0.6">
    <path d="M960,100 Q980,150 960,200 Q940,150 960,100" stroke="#ffffff" stroke-width="3" fill="none"/>
    <path d="M960,800 Q980,750 960,700 Q940,750 960,800" stroke="#ffffff" stroke-width="3" fill="none"/>
  </g>
  
  <!-- 底部成就感效果 -->
  <g transform="translate(960,900)" opacity="0.8">
    <path d="M-200,0 Q-100,-40 0,0 Q100,-40 200,0" stroke="#ffffff" stroke-width="5" fill="none"/>
    <circle cx="-150" cy="-10" r="5" fill="#ffffff"/>
    <circle cx="0" cy="-30" r="5" fill="#ffffff"/>
    <circle cx="150" cy="-10" r="5" fill="#ffffff"/>
  </g>
</svg>
