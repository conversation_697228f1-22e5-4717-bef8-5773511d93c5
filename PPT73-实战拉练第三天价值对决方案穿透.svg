<?xml version="1.0" encoding="UTF-8"?>
<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景渐变 -->
  <defs>
    <linearGradient id="surgicalGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#0d47a1;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#1565c0;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#1976d2;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="url(#surgicalGradient)"/>
  
  <!-- 手术刀背景 -->
  <g transform="translate(960,400)">
    <!-- 手术刀刀身 -->
    <path d="M-200,-100 L200,100 L190,110 L-210,-90 Z" fill="#e0e0e0" opacity="0.9"/>
    <path d="M-190,-90 L190,110 L180,120 L-200,-80 Z" fill="#f5f5f5"/>
    
    <!-- 刀柄 -->
    <rect x="-250" y="-120" width="60" height="40" rx="20" fill="#37474f"/>
    <rect x="-240" y="-110" width="40" height="20" fill="#455a64"/>
    
    <!-- 寒光效果 -->
    <g opacity="0.8">
      <path d="M-100,-50 L100,50 L95,55 L-105,-45 Z" fill="#ffffff" opacity="0.6"/>
      <path d="M-50,-25 L50,25 L45,30 L-55,-20 Z" fill="#e3f2fd" opacity="0.8"/>
    </g>
    
    <!-- 复杂网络节点 -->
    <g transform="translate(150,-150)" opacity="0.7">
      <circle cx="0" cy="0" r="30" fill="#ffab40" opacity="0.8"/>
      <circle cx="-40" cy="-20" r="20" fill="#ff9800" opacity="0.6"/>
      <circle cx="40" cy="-20" r="20" fill="#ff9800" opacity="0.6"/>
      <circle cx="-20" cy="30" r="15" fill="#ffc107" opacity="0.6"/>
      <circle cx="20" cy="30" r="15" fill="#ffc107" opacity="0.6"/>
      
      <!-- 连接线 -->
      <line x1="0" y1="0" x2="-40" y2="-20" stroke="#ffffff" stroke-width="2"/>
      <line x1="0" y1="0" x2="40" y2="-20" stroke="#ffffff" stroke-width="2"/>
      <line x1="0" y1="0" x2="-20" y2="30" stroke="#ffffff" stroke-width="2"/>
      <line x1="0" y1="0" x2="20" y2="30" stroke="#ffffff" stroke-width="2"/>
      
      <!-- 发光效果 -->
      <circle cx="0" cy="0" r="35" fill="none" stroke="#ffeb3b" stroke-width="2" opacity="0.6"/>
    </g>
  </g>
  
  <!-- 模块标题 -->
  <text x="960" y="200" text-anchor="middle" font-family="Microsoft YaHei" font-size="48" font-weight="bold" fill="#ffffff">
    实战拉练 · 第三天
  </text>
  <text x="960" y="280" text-anchor="middle" font-family="Microsoft YaHei" font-size="72" font-weight="bold" fill="#ffab40">
    价值对决 · 方案穿透
  </text>
  
  <!-- 副标题 -->
  <text x="960" y="750" text-anchor="middle" font-family="Microsoft YaHei" font-size="42" fill="#ffffff" opacity="0.8">
    ——从情报到攻击，一场价值的正面交锋
  </text>
  
  <!-- 装饰性元素 -->
  <g opacity="0.3">
    <circle cx="200" cy="200" r="4" fill="#ffab40"/>
    <circle cx="1720" cy="200" r="4" fill="#ffab40"/>
    <circle cx="200" cy="880" r="4" fill="#ffab40"/>
    <circle cx="1720" cy="880" r="4" fill="#ffab40"/>
  </g>
  
  <!-- 战术光环 -->
  <circle cx="960" cy="400" r="450" fill="none" stroke="#ffab40" stroke-width="3" opacity="0.4"/>
  <circle cx="960" cy="400" r="480" fill="none" stroke="#ffffff" stroke-width="1" opacity="0.3"/>
  
  <!-- 精准切入效果 -->
  <g opacity="0.6">
    <path d="M400,300 L420,280 L400,260" stroke="#ffab40" stroke-width="3" fill="none"/>
    <path d="M1520,300 L1500,280 L1520,260" stroke="#ffab40" stroke-width="3" fill="none"/>
    <path d="M400,780 L420,800 L400,820" stroke="#ffab40" stroke-width="3" fill="none"/>
    <path d="M1520,780 L1500,800 L1520,820" stroke="#ffab40" stroke-width="3" fill="none"/>
  </g>
</svg>
