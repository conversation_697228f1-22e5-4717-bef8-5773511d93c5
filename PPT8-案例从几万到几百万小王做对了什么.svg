<?xml version="1.0" encoding="UTF-8"?>
<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="#ffffff"/>
  
  <!-- 装饰性弧线背景 -->
  <path d="M0,100 Q960,50 1920,100" stroke="#e8f5e8" stroke-width="25" fill="none" opacity="0.3"/>
  
  <!-- 标题 -->
  <text x="960" y="100" text-anchor="middle" font-family="Microsoft YaHei" font-size="56" font-weight="bold" fill="#2e7d32">
    【案例】从几万到几百万，小王做对了什么？
  </text>
  
  <!-- 故事线流程 -->
  <!-- 起点：客户的原始需求 -->
  <g transform="translate(200,200)">
    <rect x="0" y="0" width="400" height="150" rx="15" fill="#ffebee" opacity="0.3"/>
    <text x="200" y="30" text-anchor="middle" font-family="Microsoft YaHei" font-size="28" font-weight="bold" fill="#d32f2f">起点：客户的原始需求</text>
    
    <text x="20" y="70" font-family="Microsoft YaHei" font-size="24" fill="#424242">某机械制造厂：</text>
    <text x="20" y="100" font-family="Microsoft YaHei" font-size="22" fill="#424242">"小王，我们厂区网络太差，</text>
    <text x="20" y="125" font-family="Microsoft YaHei" font-size="22" fill="#424242">给我们拉条快点的专线吧。"</text>
    
    <!-- 普通网络布线图 -->
    <g transform="translate(320,80)">
      <rect x="0" y="0" width="60" height="40" fill="#757575" opacity="0.6"/>
      <line x1="10" y1="10" x2="50" y2="10" stroke="#424242" stroke-width="2"/>
      <line x1="10" y1="20" x2="50" y2="20" stroke="#424242" stroke-width="2"/>
      <line x1="10" y1="30" x2="50" y2="30" stroke="#424242" stroke-width="2"/>
    </g>
  </g>
  
  <!-- 箭头1 -->
  <path d="M650,275 L750,275" stroke="#4caf50" stroke-width="4" marker-end="url(#arrow1)"/>
  
  <!-- 转折点：从"响应需求"到"探寻痛点" -->
  <g transform="translate(800,200)">
    <rect x="0" y="0" width="500" height="200" rx="15" fill="#fff3e0" opacity="0.3"/>
    <text x="250" y="30" text-anchor="middle" font-family="Microsoft YaHei" font-size="28" font-weight="bold" fill="#f57c00">转折点：从"响应需求"到"探寻痛点"</text>
    
    <text x="20" y="70" font-family="Microsoft YaHei" font-size="24" fill="#424242">小王没有急于报价，而是深入车间，</text>
    <text x="20" y="100" font-family="Microsoft YaHei" font-size="24" fill="#424242">发现三大核心痛点：</text>
    
    <circle cx="40" cy="130" r="6" fill="#ff9800"/>
    <text x="60" y="140" font-family="Microsoft YaHei" font-size="22" fill="#424242">设备经常坏</text>
    
    <circle cx="40" cy="155" r="6" fill="#ff9800"/>
    <text x="60" y="165" font-family="Microsoft YaHei" font-size="22" fill="#424242">产品次品多</text>
    
    <circle cx="40" cy="180" r="6" fill="#ff9800"/>
    <text x="60" y="190" font-family="Microsoft YaHei" font-size="22" fill="#424242">生产数据乱</text>
  </g>
  
  <!-- 箭头2 -->
  <path d="M1050,420 L1050,520" stroke="#4caf50" stroke-width="4" marker-end="url(#arrow1)"/>
  
  <!-- 价值跃迁：提交解决方案 -->
  <g transform="translate(750,550)">
    <rect x="0" y="0" width="600" height="250" rx="15" fill="#e8f5e8" opacity="0.3"/>
    <text x="300" y="30" text-anchor="middle" font-family="Microsoft YaHei" font-size="28" font-weight="bold" fill="#2e7d32">价值跃迁：提交《5G全连接智慧工厂解决方案》</text>
    
    <circle cx="40" cy="70" r="6" fill="#4caf50"/>
    <text x="60" y="80" font-family="Microsoft YaHei" font-size="22" fill="#424242">连接：5G专网 + 物联网卡</text>
    
    <circle cx="40" cy="100" r="6" fill="#4caf50"/>
    <text x="60" y="110" font-family="Microsoft YaHei" font-size="22" fill="#424242">算力：边缘计算(MEC) + AI质检</text>
    
    <circle cx="40" cy="130" r="6" fill="#4caf50"/>
    <text x="60" y="140" font-family="Microsoft YaHei" font-size="22" fill="#424242">能力：大数据预测性维护 + 手机端"数字驾驶舱"</text>
    
    <!-- 智慧工厂大屏 -->
    <g transform="translate(400,80)">
      <rect x="0" y="0" width="150" height="100" fill="#263238" opacity="0.8"/>
      <rect x="10" y="10" width="130" height="80" fill="#37474f"/>
      <rect x="20" y="20" width="30" height="25" fill="#4caf50" opacity="0.7"/>
      <rect x="60" y="20" width="30" height="25" fill="#2196f3" opacity="0.7"/>
      <rect x="100" y="20" width="30" height="25" fill="#ff9800" opacity="0.7"/>
      <rect x="20" y="55" width="110" height="15" fill="#9c27b0" opacity="0.7"/>
    </g>
  </g>
  
  <!-- 箭头3 -->
  <path d="M650,825 L750,825" stroke="#4caf50" stroke-width="4" marker-end="url(#arrow1)"/>
  
  <!-- 结果 -->
  <g transform="translate(800,850)">
    <rect x="0" y="0" width="500" height="80" rx="15" fill="#e3f2fd" opacity="0.3"/>
    <text x="250" y="30" text-anchor="middle" font-family="Microsoft YaHei" font-size="28" font-weight="bold" fill="#1565c0">结果：从"网络供应商"升级为"数字化转型伙伴"</text>
    <text x="250" y="60" text-anchor="middle" font-family="Microsoft YaHei" font-size="24" fill="#424242">几万 → 几百万</text>
  </g>
  
  <!-- 箭头标记定义 -->
  <defs>
    <marker id="arrow1" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#4caf50"/>
    </marker>
  </defs>
</svg>
