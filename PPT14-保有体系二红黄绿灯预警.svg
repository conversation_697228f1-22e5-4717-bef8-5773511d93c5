<?xml version="1.0" encoding="UTF-8"?>
<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="#ffffff"/>
  
  <!-- 装饰性弧线背景 -->
  <path d="M0,100 Q960,50 1920,100" stroke="#f3e5f5" stroke-width="25" fill="none" opacity="0.3"/>
  
  <!-- 标题 -->
  <text x="960" y="120" text-anchor="middle" font-family="Microsoft YaHei" font-size="56" font-weight="bold" fill="#7b1fa2">
    保有体系二：客户健康度"红黄绿"灯预警
  </text>
  
  <!-- 交通信号灯主体 -->
  <g transform="translate(400,400)">
    <!-- 信号灯外框 -->
    <rect x="0" y="0" width="120" height="320" rx="20" fill="#424242"/>
    <circle cx="60" cy="80" r="40" fill="#4caf50"/>
    <circle cx="60" cy="160" r="40" fill="#ffeb3b"/>
    <circle cx="60" cy="240" r="40" fill="#f44336"/>
  </g>
  
  <!-- 绿灯 (健康客户) -->
  <g transform="translate(600,300)">
    <rect x="0" y="0" width="500" height="150" rx="15" fill="#e8f5e8" opacity="0.3"/>
    <text x="250" y="30" text-anchor="middle" font-family="Microsoft YaHei" font-size="36" font-weight="bold" fill="#2e7d32">绿灯 (健康客户)</text>
    
    <text x="20" y="70" font-family="Microsoft YaHei" font-size="24" font-weight="bold" fill="#2e7d32">状态：</text>
    <text x="100" y="70" font-family="Microsoft YaHei" font-size="22" fill="#424242">指标正常，满意度高</text>
    
    <text x="20" y="110" font-family="Microsoft YaHei" font-size="24" font-weight="bold" fill="#2e7d32">动作：</text>
    <text x="100" y="110" font-family="Microsoft YaHei" font-size="22" fill="#424242">挖掘增值机会 (交叉/向上销售)</text>
  </g>
  
  <!-- 黄灯 (预警客户) -->
  <g transform="translate(600,480)">
    <rect x="0" y="0" width="500" height="180" rx="15" fill="#fff8e1" opacity="0.3"/>
    <text x="250" y="30" text-anchor="middle" font-family="Microsoft YaHei" font-size="36" font-weight="bold" fill="#f57c00">黄灯 (预警客户)</text>
    
    <text x="20" y="70" font-family="Microsoft YaHei" font-size="24" font-weight="bold" fill="#f57c00">状态：</text>
    <text x="100" y="70" font-family="Microsoft YaHei" font-size="22" fill="#424242">出现1-2项预警信号（如消费异动、竞品接触），</text>
    <text x="100" y="100" font-family="Microsoft YaHei" font-size="22" fill="#424242">满意度下滑</text>
    
    <text x="20" y="140" font-family="Microsoft YaHei" font-size="24" font-weight="bold" fill="#f57c00">动作：</text>
    <text x="100" y="140" font-family="Microsoft YaHei" font-size="22" fill="#424242">启动SOP，专项小组介入 (72小时内出诊断报告)</text>
  </g>
  
  <!-- 红灯 (高危客户) -->
  <g transform="translate(600,690)">
    <rect x="0" y="0" width="500" height="180" rx="15" fill="#ffebee" opacity="0.3"/>
    <text x="250" y="30" text-anchor="middle" font-family="Microsoft YaHei" font-size="36" font-weight="bold" fill="#d32f2f">红灯 (高危客户)</text>
    
    <text x="20" y="70" font-family="Microsoft YaHei" font-size="24" font-weight="bold" fill="#d32f2f">状态：</text>
    <text x="100" y="70" font-family="Microsoft YaHei" font-size="22" fill="#424242">出现多项强预警信号，或已明确表达离网意向</text>
    
    <text x="20" y="120" font-family="Microsoft YaHei" font-size="24" font-weight="bold" fill="#d32f2f">动作：</text>
    <text x="100" y="120" font-family="Microsoft YaHei" font-size="22" fill="#424242">最高级别响应，领导挂帅 ("一户一策"攻坚挽留)</text>
  </g>
  
  <!-- 右侧流程图 -->
  <g transform="translate(1200,400)">
    <rect x="0" y="0" width="400" height="400" rx="15" fill="#f5f5f5" opacity="0.3"/>
    <text x="200" y="30" text-anchor="middle" font-family="Microsoft YaHei" font-size="28" font-weight="bold" fill="#424242">预警处理流程</text>
    
    <!-- 流程步骤 -->
    <rect x="50" y="60" width="300" height="40" rx="5" fill="#4caf50" opacity="0.7"/>
    <text x="200" y="85" text-anchor="middle" font-family="Microsoft YaHei" font-size="18" fill="#ffffff">1. 数据监控发现异常</text>
    
    <path d="M200,100 L200,120" stroke="#424242" stroke-width="2" marker-end="url(#flowArrow)"/>
    
    <rect x="50" y="120" width="300" height="40" rx="5" fill="#ffeb3b" opacity="0.7"/>
    <text x="200" y="145" text-anchor="middle" font-family="Microsoft YaHei" font-size="18" fill="#424242">2. 启动预警机制</text>
    
    <path d="M200,160 L200,180" stroke="#424242" stroke-width="2" marker-end="url(#flowArrow)"/>
    
    <rect x="50" y="180" width="300" height="40" rx="5" fill="#ff9800" opacity="0.7"/>
    <text x="200" y="205" text-anchor="middle" font-family="Microsoft YaHei" font-size="18" fill="#ffffff">3. 专项小组介入</text>
    
    <path d="M200,220 L200,240" stroke="#424242" stroke-width="2" marker-end="url(#flowArrow)"/>
    
    <rect x="50" y="240" width="300" height="40" rx="5" fill="#2196f3" opacity="0.7"/>
    <text x="200" y="265" text-anchor="middle" font-family="Microsoft YaHei" font-size="18" fill="#ffffff">4. 72小时出诊断</text>
    
    <path d="M200,280 L200,300" stroke="#424242" stroke-width="2" marker-end="url(#flowArrow)"/>
    
    <rect x="50" y="300" width="300" height="40" rx="5" fill="#9c27b0" opacity="0.7"/>
    <text x="200" y="325" text-anchor="middle" font-family="Microsoft YaHei" font-size="18" fill="#ffffff">5. 制定维系方案</text>
  </g>
  
  <!-- 连接线 -->
  <line x1="520" y1="380" x2="580" y2="380" stroke="#4caf50" stroke-width="3"/>
  <line x1="520" y1="480" x2="580" y2="570" stroke="#ffeb3b" stroke-width="3"/>
  <line x1="520" y1="560" x2="580" y2="780" stroke="#f44336" stroke-width="3"/>
  
  <!-- 箭头标记定义 -->
  <defs>
    <marker id="flowArrow" markerWidth="8" markerHeight="6" refX="7" refY="3" orient="auto">
      <polygon points="0 0, 8 3, 0 6" fill="#424242"/>
    </marker>
  </defs>
</svg>
