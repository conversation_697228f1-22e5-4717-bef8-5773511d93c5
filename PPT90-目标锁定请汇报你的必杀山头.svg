<?xml version="1.0" encoding="UTF-8"?>
<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="#ffffff"/>
  
  <!-- 装饰性弧线背景 -->
  <path d="M0,100 Q960,50 1920,100" stroke="#ffebee" stroke-width="25" fill="none" opacity="0.3"/>
  
  <!-- 标题 -->
  <text x="960" y="120" text-anchor="middle" font-family="Microsoft YaHei" font-size="48" font-weight="bold" fill="#d32f2f">
    目标锁定：请汇报你的"必杀山头"！
  </text>
  
  <!-- 巨大的红色靶心 -->
  <g transform="translate(960,350)">
    <!-- 靶心圆环 -->
    <circle cx="0" cy="0" r="120" fill="#f44336" opacity="0.8"/>
    <circle cx="0" cy="0" r="100" fill="#ffffff"/>
    <circle cx="0" cy="0" r="80" fill="#f44336" opacity="0.8"/>
    <circle cx="0" cy="0" r="60" fill="#ffffff"/>
    <circle cx="0" cy="0" r="40" fill="#f44336" opacity="0.8"/>
    <circle cx="0" cy="0" r="20" fill="#ffffff"/>
    
    <!-- 中心"签约！" -->
    <text x="0" y="10" text-anchor="middle" font-family="Microsoft YaHei" font-size="32" font-weight="bold" fill="#d32f2f">签约！</text>
    
    <!-- 瞄准十字线 -->
    <g stroke="#d32f2f" stroke-width="3">
      <line x1="-140" y1="0" x2="-125" y2="0"/>
      <line x1="125" y1="0" x2="140" y2="0"/>
      <line x1="0" y1="-140" x2="0" y2="-125"/>
      <line x1="0" y1="125" x2="0" y2="140"/>
    </g>
    
    <!-- 瞄准镜效果 -->
    <circle cx="0" cy="0" r="150" fill="none" stroke="#f44336" stroke-width="2" opacity="0.5"/>
    <circle cx="0" cy="0" r="170" fill="none" stroke="#ffeb3b" stroke-width="1" opacity="0.4"/>
  </g>
  
  <!-- 三个问题环绕靶心 -->
  <g transform="translate(960,350)">
    <!-- 问题1 -->
    <g transform="translate(-350,-200)">
      <rect x="-150" y="-40" width="300" height="80" rx="15" fill="#e3f2fd" opacity="0.8"/>
      <text x="0" y="-15" text-anchor="middle" font-family="Microsoft YaHei" font-size="20" font-weight="bold" fill="#1565c0">1</text>
      <text x="0" y="10" text-anchor="middle" font-family="Microsoft YaHei" font-size="18" fill="#424242">今天上午，你们必须拿下的那个</text>
      <text x="0" y="30" text-anchor="middle" font-family="Microsoft YaHei" font-size="18" font-weight="bold" fill="#1565c0">"山头"是哪一个客户？</text>
    </g>
    
    <!-- 问题2 -->
    <g transform="translate(350,-200)">
      <rect x="-150" y="-40" width="300" height="80" rx="15" fill="#fff3e0" opacity="0.8"/>
      <text x="0" y="-15" text-anchor="middle" font-family="Microsoft YaHei" font-size="20" font-weight="bold" fill="#f57c00">2</text>
      <text x="0" y="10" text-anchor="middle" font-family="Microsoft YaHei" font-size="18" fill="#424242">你判断，今天签单的可能性是</text>
      <text x="0" y="30" text-anchor="middle" font-family="Microsoft YaHei" font-size="18" font-weight="bold" fill="#f57c00">80%、90%，还是100%？</text>
    </g>
    
    <!-- 问题3 -->
    <g transform="translate(0,250)">
      <rect x="-200" y="-40" width="400" height="80" rx="15" fill="#e8f5e8" opacity="0.8"/>
      <text x="0" y="-15" text-anchor="middle" font-family="Microsoft YaHei" font-size="20" font-weight="bold" fill="#2e7d32">3</text>
      <text x="0" y="10" text-anchor="middle" font-family="Microsoft YaHei" font-size="18" fill="#424242">你准备用什么方法，来完成这</text>
      <text x="0" y="30" text-anchor="middle" font-family="Microsoft YaHei" font-size="18" font-weight="bold" fill="#2e7d32">决定胜负的最后10%？</text>
    </g>
    
    <!-- 连接线 -->
    <g stroke="#d32f2f" stroke-width="2" opacity="0.5">
      <line x1="-200" y1="-160" x2="-80" y2="-80"/>
      <line x1="200" y1="-160" x2="80" y2="-80"/>
      <line x1="0" y1="210" x2="0" y2="120"/>
    </g>
  </g>
  
  <!-- 装饰性瞄准器 -->
  <g transform="translate(200,600)" opacity="0.4">
    <circle cx="0" cy="0" r="25" fill="none" stroke="#d32f2f" stroke-width="3"/>
    <line x1="-30" y1="0" x2="-27" y2="0" stroke="#d32f2f" stroke-width="2"/>
    <line x1="27" y1="0" x2="30" y2="0" stroke="#d32f2f" stroke-width="2"/>
    <line x1="0" y1="-30" x2="0" y2="-27" stroke="#d32f2f" stroke-width="2"/>
    <line x1="0" y1="27" x2="0" y2="30" stroke="#d32f2f" stroke-width="2"/>
    <circle cx="0" cy="0" r="5" fill="#d32f2f"/>
  </g>
  
  <g transform="translate(1720,600)" opacity="0.4">
    <circle cx="0" cy="0" r="25" fill="none" stroke="#d32f2f" stroke-width="3"/>
    <line x1="-30" y1="0" x2="-27" y2="0" stroke="#d32f2f" stroke-width="2"/>
    <line x1="27" y1="0" x2="30" y2="0" stroke="#d32f2f" stroke-width="2"/>
    <line x1="0" y1="-30" x2="0" y2="-27" stroke="#d32f2f" stroke-width="2"/>
    <line x1="0" y1="27" x2="0" y2="30" stroke="#d32f2f" stroke-width="2"/>
    <circle cx="0" cy="0" r="5" fill="#d32f2f"/>
  </g>
</svg>
