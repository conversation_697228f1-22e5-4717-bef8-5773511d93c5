<?xml version="1.0" encoding="UTF-8"?>
<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="#ffffff"/>
  
  <!-- 装饰性弧线背景 -->
  <path d="M0,100 Q960,50 1920,100" stroke="#ffebee" stroke-width="25" fill="none" opacity="0.3"/>
  
  <!-- 标题 -->
  <text x="960" y="120" text-anchor="middle" font-family="Microsoft YaHei" font-size="48" font-weight="bold" fill="#d32f2f">
    我们今天遇到的"拦路虎"
  </text>
  
  <!-- 中央巨大的"但是..."对话框 -->
  <g transform="translate(960,350)">
    <!-- 带刺对话框 -->
    <path d="M-150,-80 Q-170,-100 -150,-120 L150,-120 Q170,-100 150,-80 L150,80 Q170,100 150,120 L-150,120 Q-170,100 -150,80 Z" 
          fill="#f44336" opacity="0.8"/>
    <path d="M-140,-70 Q-160,-90 -140,-110 L140,-110 Q160,-90 140,-70 L140,70 Q160,90 140,110 L-140,110 Q-160,90 -140,70 Z" 
          fill="#ffffff"/>
    
    <!-- 刺 -->
    <polygon points="-180,-60 -160,-50 -180,-40" fill="#f44336"/>
    <polygon points="180,-60 160,-50 180,-40" fill="#f44336"/>
    <polygon points="-180,60 -160,50 -180,40" fill="#f44336"/>
    <polygon points="180,60 160,50 180,40" fill="#f44336"/>
    
    <!-- "但是..." 文字 -->
    <text x="0" y="10" text-anchor="middle" font-family="Microsoft YaHei" font-size="48" font-weight="bold" fill="#d32f2f">但是...</text>
  </g>
  
  <!-- 环绕的常见异议 -->
  <g transform="translate(960,350)">
    <!-- 异议1 -->
    <g transform="translate(-400,-200)">
      <ellipse cx="0" cy="0" rx="120" ry="40" fill="#ffcdd2" opacity="0.8"/>
      <text x="0" y="-10" text-anchor="middle" font-family="Microsoft YaHei" font-size="16" fill="#d32f2f">"你们的价格比XX贵了30%！"</text>
    </g>
    
    <!-- 异议2 -->
    <g transform="translate(400,-200)">
      <ellipse cx="0" cy="0" rx="140" ry="40" fill="#ffcdd2" opacity="0.8"/>
      <text x="0" y="-10" text-anchor="middle" font-family="Microsoft YaHei" font-size="16" fill="#d32f2f">"我们现在用对手的挺好的，没必要换。"</text>
    </g>
    
    <!-- 异议3 -->
    <g transform="translate(-400,200)">
      <ellipse cx="0" cy="0" rx="130" ry="40" fill="#ffcdd2" opacity="0.8"/>
      <text x="0" y="-10" text-anchor="middle" font-family="Microsoft YaHei" font-size="16" fill="#d32f2f">"这个方案不错，但我们今年没预算了。"</text>
    </g>
    
    <!-- 异议4 -->
    <g transform="translate(400,200)">
      <ellipse cx="0" cy="0" rx="120" ry="40" fill="#ffcdd2" opacity="0.8"/>
      <text x="0" y="-10" text-anchor="middle" font-family="Microsoft YaHei" font-size="16" fill="#d32f2f">"我需要和老板/团队商量一下。"</text>
    </g>
    
    <!-- 异议5 -->
    <g transform="translate(0,280)">
      <ellipse cx="0" cy="0" rx="130" ry="40" fill="#ffcdd2" opacity="0.8"/>
      <text x="0" y="-10" text-anchor="middle" font-family="Microsoft YaHei" font-size="16" fill="#d32f2f">"你说的这些功能，我们好像用不上。"</text>
    </g>
    
    <!-- 连接线 -->
    <g stroke="#f44336" stroke-width="2" opacity="0.5">
      <line x1="-280" y1="-160" x2="-120" y2="-60"/>
      <line x1="280" y1="-160" x2="120" y2="-60"/>
      <line x1="-280" y1="160" x2="-120" y2="60"/>
      <line x1="280" y1="160" x2="120" y2="60"/>
      <line x1="0" y1="240" x2="0" y2="120"/>
    </g>
  </g>
  
  <!-- 底部大问号 -->
  <g transform="translate(960,800)">
    <rect x="-500" y="-80" width="1000" height="160" rx="15" fill="#ffebee" opacity="0.3"/>
    <text x="0" y="-30" text-anchor="middle" font-family="Microsoft YaHei" font-size="36" font-weight="bold" fill="#d32f2f">
      听到这些，你的第一反应是"恐惧"还是"兴奋"？
    </text>
    
    <!-- 巨大问号 -->
    <g transform="translate(0,30)">
      <circle cx="0" cy="0" r="40" fill="#f44336" opacity="0.8"/>
      <text x="0" y="15" text-anchor="middle" font-family="Microsoft YaHei" font-size="48" font-weight="bold" fill="#ffffff">?</text>
    </g>
  </g>
  
  <!-- 装饰性警告图标 -->
  <g transform="translate(200,400)" opacity="0.4">
    <polygon points="0,-20 -17,15 17,15" fill="#f44336"/>
    <circle cx="0" cy="10" r="8" fill="#ffffff"/>
    <text x="0" y="15" text-anchor="middle" font-family="Microsoft YaHei" font-size="12" font-weight="bold" fill="#f44336">!</text>
  </g>
  
  <g transform="translate(1720,400)" opacity="0.4">
    <polygon points="0,-20 -17,15 17,15" fill="#f44336"/>
    <circle cx="0" cy="10" r="8" fill="#ffffff"/>
    <text x="0" y="15" text-anchor="middle" font-family="Microsoft YaHei" font-size="12" font-weight="bold" fill="#f44336">!</text>
  </g>
</svg>
