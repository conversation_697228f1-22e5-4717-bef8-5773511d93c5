<?xml version="1.0" encoding="UTF-8"?>
<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="#ffffff"/>
  
  <!-- 装饰性弧线背景 -->
  <path d="M0,100 Q960,50 1920,100" stroke="#e3f2fd" stroke-width="25" fill="none" opacity="0.3"/>
  
  <!-- 标题 -->
  <text x="960" y="120" text-anchor="middle" font-family="Microsoft YaHei" font-size="48" font-weight="bold" fill="#1565c0">
    魔鬼客户一：技术细节控 (IT总监)
  </text>
  
  <!-- 大脑与电路板结合图标 -->
  <g transform="translate(960,300)">
    <!-- 大脑轮廓 -->
    <path d="M-60,-40 Q-80,-60 -60,-80 Q-20,-90 20,-80 Q60,-60 60,-40 Q80,-20 60,0 Q40,20 20,40 Q-20,40 -40,20 Q-80,0 -60,-40" 
          fill="#2196f3" opacity="0.8"/>
    
    <!-- 电路板纹理 -->
    <g stroke="#ffffff" stroke-width="2" fill="none">
      <line x1="-40" y1="-60" x2="40" y2="-60"/>
      <line x1="-30" y1="-40" x2="30" y2="-40"/>
      <line x1="-35" y1="-20" x2="35" y2="-20"/>
      <line x1="-25" y1="0" x2="25" y2="0"/>
      <line x1="-20" y1="20" x2="20" y2="20"/>
      
      <!-- 垂直线 -->
      <line x1="-20" y1="-70" x2="-20" y2="30"/>
      <line x1="0" y1="-70" x2="0" y2="30"/>
      <line x1="20" y1="-70" x2="20" y2="30"/>
    </g>
    
    <!-- 电路节点 -->
    <circle cx="-20" cy="-60" r="3" fill="#ffeb3b"/>
    <circle cx="0" cy="-40" r="3" fill="#ffeb3b"/>
    <circle cx="20" cy="-20" r="3" fill="#ffeb3b"/>
    <circle cx="-15" cy="0" r="3" fill="#ffeb3b"/>
    <circle cx="15" cy="20" r="3" fill="#ffeb3b"/>
    
    <!-- 数据流动效果 -->
    <g opacity="0.6">
      <circle cx="-30" cy="-50" r="2" fill="#4caf50"/>
      <circle cx="10" cy="-30" r="2" fill="#4caf50"/>
      <circle cx="-10" cy="-10" r="2" fill="#4caf50"/>
      <circle cx="25" cy="10" r="2" fill="#4caf50"/>
    </g>
  </g>
  
  <!-- 口头禅 -->
  <g transform="translate(960,450)">
    <rect x="-400" y="-40" width="800" height="80" rx="15" fill="#e3f2fd" opacity="0.3"/>
    <text x="0" y="-10" text-anchor="middle" font-family="Microsoft YaHei" font-size="24" font-weight="bold" fill="#1565c0">他的口头禅：</text>
    <text x="0" y="25" text-anchor="middle" font-family="Microsoft YaHei" font-size="28" font-weight="bold" fill="#d32f2f">"不要跟我说感觉，给我看数据！"</text>
  </g>
  
  <!-- 他关心的问题 -->
  <g transform="translate(960,650)">
    <rect x="-700" y="-150" width="1400" height="300" rx="20" fill="#e3f2fd" opacity="0.3"/>
    <text x="0" y="-110" text-anchor="middle" font-family="Microsoft YaHei" font-size="32" font-weight="bold" fill="#1565c0">他关心的问题</text>
    
    <g transform="translate(-500,-50)">
      <circle cx="0" cy="0" r="6" fill="#1565c0"/>
      <text x="20" y="5" font-family="Microsoft YaHei" font-size="18" fill="#424242">"你凭什么说能帮我司效率提升20%？这个数据哪里来的？有经过第三方验证吗？"</text>
    </g>
    
    <g transform="translate(-500,-10)">
      <circle cx="0" cy="0" r="6" fill="#1565c0"/>
      <text x="20" y="5" font-family="Microsoft YaHei" font-size="18" fill="#424242">"你说的云平台，它的底层架构是什么？能保证99.99%的可用性吗？"</text>
    </g>
    
    <g transform="translate(-500,30)">
      <circle cx="0" cy="0" r="6" fill="#1565c0"/>
      <text x="20" y="5" font-family="Microsoft YaHei" font-size="18" fill="#424242">"如果达不到承诺的SLA，惩罚条款是什么？写在合同里吗？"</text>
    </g>
    
    <g transform="translate(-500,70)">
      <circle cx="0" cy="0" r="6" fill="#1565c0"/>
      <text x="20" y="5" font-family="Microsoft YaHei" font-size="18" fill="#424242">"你说的那个数据加密，是AES256的吗？密钥管理方案是怎样的？"</text>
    </g>
  </g>
  
  <!-- 装饰性技术图标 -->
  <g transform="translate(200,400)" opacity="0.4">
    <rect x="0" y="0" width="40" height="30" fill="#1565c0"/>
    <rect x="5" y="5" width="30" height="20" fill="#ffffff"/>
    <text x="20" y="20" text-anchor="middle" font-family="Microsoft YaHei" font-size="12" font-weight="bold" fill="#1565c0">01</text>
  </g>
  
  <g transform="translate(1720,400)" opacity="0.4">
    <rect x="-40" y="0" width="40" height="30" fill="#1565c0"/>
    <rect x="-35" y="5" width="30" height="20" fill="#ffffff"/>
    <text x="-20" y="20" text-anchor="middle" font-family="Microsoft YaHei" font-size="12" font-weight="bold" fill="#1565c0">10</text>
  </g>
</svg>
