<?xml version="1.0" encoding="UTF-8"?>
<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="#ffffff"/>
  
  <!-- 装饰性弧线背景 -->
  <path d="M0,100 Q960,50 1920,100" stroke="#e3f2fd" stroke-width="25" fill="none" opacity="0.3"/>
  
  <!-- 标题 -->
  <text x="960" y="120" text-anchor="middle" font-family="Microsoft YaHei" font-size="48" font-weight="bold" fill="#1565c0">
    第一环节：战地情报汇报
  </text>
  
  <!-- 副标题 -->
  <text x="960" y="180" text-anchor="middle" font-family="Microsoft YaHei" font-size="32" fill="#424242">
    展示你的《集团客户作战地图》V1.0
  </text>
  
  <!-- 中央作战地图图标 -->
  <g transform="translate(960,400)">
    <rect x="-150" y="-100" width="300" height="200" rx="15" fill="#1565c0" opacity="0.8"/>
    <rect x="-140" y="-90" width="280" height="180" fill="#ffffff"/>
    
    <!-- 地图网格 -->
    <g stroke="#1565c0" stroke-width="1" opacity="0.3">
      <line x1="-120" y1="-70" x2="120" y2="-70"/>
      <line x1="-120" y1="-30" x2="120" y2="-30"/>
      <line x1="-120" y1="10" x2="120" y2="10"/>
      <line x1="-120" y1="50" x2="120" y2="50"/>
      <line x1="-80" y1="-90" x2="-80" y2="90"/>
      <line x1="-40" y1="-90" x2="-40" y2="90"/>
      <line x1="0" y1="-90" x2="0" y2="90"/>
      <line x1="40" y1="-90" x2="40" y2="90"/>
      <line x1="80" y1="-90" x2="80" y2="90"/>
    </g>
    
    <!-- 目标标记 -->
    <circle cx="60" cy="-20" r="12" fill="#f44336" opacity="0.8"/>
    <circle cx="60" cy="-20" r="8" fill="#ffffff"/>
    <circle cx="60" cy="-20" r="4" fill="#f44336"/>
    
    <!-- 路线标记 -->
    <path d="M-100,60 Q-20,20 60,-20" stroke="#4caf50" stroke-width="3" fill="none" stroke-dasharray="5,3"/>
  </g>
  
  <!-- 四个关键词环绕 -->
  <g transform="translate(960,400)">
    <!-- 核心情报 -->
    <g transform="translate(-250,-150)">
      <circle cx="0" cy="0" r="40" fill="#4caf50" opacity="0.8"/>
      <circle cx="0" cy="0" r="30" fill="#ffffff"/>
      <text x="0" y="5" text-anchor="middle" font-family="Microsoft YaHei" font-size="16" font-weight="bold" fill="#2e7d32">核心情报</text>
    </g>
    
    <!-- 关键突破 -->
    <g transform="translate(250,-150)">
      <circle cx="0" cy="0" r="40" fill="#ff9800" opacity="0.8"/>
      <circle cx="0" cy="0" r="30" fill="#ffffff"/>
      <text x="0" y="5" text-anchor="middle" font-family="Microsoft YaHei" font-size="16" font-weight="bold" fill="#f57c00">关键突破</text>
    </g>
    
    <!-- 初步洞察 -->
    <g transform="translate(-250,150)">
      <circle cx="0" cy="0" r="40" fill="#9c27b0" opacity="0.8"/>
      <circle cx="0" cy="0" r="30" fill="#ffffff"/>
      <text x="0" y="5" text-anchor="middle" font-family="Microsoft YaHei" font-size="16" font-weight="bold" fill="#7b1fa2">初步洞察</text>
    </g>
    
    <!-- 遗留困惑 -->
    <g transform="translate(250,150)">
      <circle cx="0" cy="0" r="40" fill="#f44336" opacity="0.8"/>
      <circle cx="0" cy="0" r="30" fill="#ffffff"/>
      <text x="0" y="5" text-anchor="middle" font-family="Microsoft YaHei" font-size="16" font-weight="bold" fill="#d32f2f">遗留困惑</text>
    </g>
  </g>
  
  <!-- 规则说明 -->
  <g transform="translate(960,750)">
    <rect x="-600" y="-50" width="1200" height="100" rx="15" fill="#e3f2fd" opacity="0.3"/>
    <text x="0" y="-10" text-anchor="middle" font-family="Microsoft YaHei" font-size="32" font-weight="bold" fill="#1565c0">
      规则：每组5分钟，汇报核心发现，并提出一个最棘手的困惑。
    </text>
  </g>
  
  <!-- 装饰性情报图标 -->
  <g transform="translate(200,300)" opacity="0.4">
    <rect x="0" y="0" width="40" height="30" fill="#1565c0"/>
    <rect x="5" y="5" width="30" height="20" fill="#ffffff"/>
    <circle cx="20" cy="15" r="8" fill="#f44336" opacity="0.6"/>
    <text x="20" y="19" text-anchor="middle" font-family="Microsoft YaHei" font-size="10" font-weight="bold" fill="#ffffff">!</text>
  </g>
  
  <g transform="translate(1720,300)" opacity="0.4">
    <rect x="-40" y="0" width="40" height="30" fill="#1565c0"/>
    <rect x="-35" y="5" width="30" height="20" fill="#ffffff"/>
    <circle cx="-20" cy="15" r="8" fill="#f44336" opacity="0.6"/>
    <text x="-20" y="19" text-anchor="middle" font-family="Microsoft YaHei" font-size="10" font-weight="bold" fill="#ffffff">!</text>
  </g>
</svg>
