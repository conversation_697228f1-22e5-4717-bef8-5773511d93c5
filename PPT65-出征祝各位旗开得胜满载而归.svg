<?xml version="1.0" encoding="UTF-8"?>
<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景渐变 -->
  <defs>
    <linearGradient id="dawnGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#1a237e;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#3949ab;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#5c6bc0;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="url(#dawnGradient)"/>
  
  <!-- 特战小队准备登机 -->
  <g transform="translate(960,400)">
    <!-- 直升机轮廓 -->
    <g transform="translate(0,-50)">
      <ellipse cx="0" cy="0" rx="200" ry="50" fill="#37474f" opacity="0.8"/>
      <rect x="-150" y="-25" width="300" height="50" fill="#455a64"/>
      <circle cx="-120" cy="-10" r="20" fill="#263238"/>
      <circle cx="120" cy="-10" r="20" fill="#263238"/>
      <!-- 螺旋桨 -->
      <ellipse cx="0" cy="-50" rx="120" ry="10" fill="#607d8b" opacity="0.6"/>
    </g>
    
    <!-- 特战小队成员 -->
    <g transform="translate(-120,80)">
      <circle cx="0" cy="-30" r="25" fill="#2e7d32" opacity="0.8"/>
      <rect x="-20" y="-5" width="40" height="50" fill="#2e7d32" opacity="0.8"/>
      <rect x="-25" y="0" width="50" height="40" fill="#1b5e20"/>
      <!-- 头盔 -->
      <ellipse cx="0" cy="-30" rx="30" ry="20" fill="#1b5e20"/>
    </g>
    
    <g transform="translate(-40,80)">
      <circle cx="0" cy="-30" r="25" fill="#2e7d32" opacity="0.8"/>
      <rect x="-20" y="-5" width="40" height="50" fill="#2e7d32" opacity="0.8"/>
      <rect x="-25" y="0" width="50" height="40" fill="#1b5e20"/>
      <ellipse cx="0" cy="-30" rx="30" ry="20" fill="#1b5e20"/>
    </g>
    
    <g transform="translate(40,80)">
      <circle cx="0" cy="-30" r="25" fill="#2e7d32" opacity="0.8"/>
      <rect x="-20" y="-5" width="40" height="50" fill="#2e7d32" opacity="0.8"/>
      <rect x="-25" y="0" width="50" height="40" fill="#1b5e20"/>
      <ellipse cx="0" cy="-30" rx="30" ry="20" fill="#1b5e20"/>
    </g>
    
    <g transform="translate(120,80)">
      <circle cx="0" cy="-30" r="25" fill="#2e7d32" opacity="0.8"/>
      <rect x="-20" y="-5" width="40" height="50" fill="#2e7d32" opacity="0.8"/>
      <rect x="-25" y="0" width="50" height="40" fill="#1b5e20"/>
      <ellipse cx="0" cy="-30" rx="30" ry="20" fill="#1b5e20"/>
    </g>
    
    <!-- 黎明曙光效果 -->
    <g opacity="0.8">
      <path d="M-500,-200 L-450,-150 L-500,-100" stroke="#ffeb3b" stroke-width="4" fill="none"/>
      <path d="M500,-200 L450,-150 L500,-100" stroke="#ffeb3b" stroke-width="4" fill="none"/>
      <circle cx="-400" cy="-180" r="4" fill="#ffeb3b"/>
      <circle cx="400" cy="-180" r="4" fill="#ffeb3b"/>
      <circle cx="-450" cy="-120" r="4" fill="#ffeb3b"/>
      <circle cx="450" cy="-120" r="4" fill="#ffeb3b"/>
    </g>
  </g>
  
  <!-- 主标题 -->
  <text x="960" y="200" text-anchor="middle" font-family="Microsoft YaHei" font-size="96" font-weight="bold" fill="#ffffff">
    出 征！
  </text>
  
  <!-- 副标题 -->
  <text x="960" y="750" text-anchor="middle" font-family="Microsoft YaHei" font-size="48" fill="#ffffff" opacity="0.9">
    祝各位，旗开得胜，满载而归！
  </text>
  
  <!-- 装饰性战斗元素 -->
  <g opacity="0.5">
    <circle cx="200" cy="200" r="8" fill="#ffffff"/>
    <circle cx="1720" cy="200" r="8" fill="#ffffff"/>
    <circle cx="200" cy="880" r="8" fill="#ffffff"/>
    <circle cx="1720" cy="880" r="8" fill="#ffffff"/>
  </g>
  
  <!-- 战斗光环 -->
  <circle cx="960" cy="400" r="500" fill="none" stroke="#ffffff" stroke-width="3" opacity="0.4"/>
  <circle cx="960" cy="400" r="530" fill="none" stroke="#ffeb3b" stroke-width="1" opacity="0.5"/>
  
  <!-- 底部集结号角效果 -->
  <g transform="translate(960,900)" opacity="0.8">
    <path d="M-150,0 Q-75,-30 0,0 Q75,-30 150,0" stroke="#ffffff" stroke-width="5" fill="none"/>
    <circle cx="-120" cy="-8" r="5" fill="#ffffff"/>
    <circle cx="0" cy="-20" r="5" fill="#ffffff"/>
    <circle cx="120" cy="-8" r="5" fill="#ffffff"/>
  </g>
</svg>
