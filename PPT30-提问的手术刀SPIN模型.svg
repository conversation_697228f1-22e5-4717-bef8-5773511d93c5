<?xml version="1.0" encoding="UTF-8"?>
<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="#ffffff"/>
  
  <!-- 装饰性弧线背景 -->
  <path d="M0,100 Q960,50 1920,100" stroke="#e1f5fe" stroke-width="25" fill="none" opacity="0.3"/>
  
  <!-- 标题 -->
  <text x="960" y="120" text-anchor="middle" font-family="Microsoft YaHei" font-size="56" font-weight="bold" fill="#0277bd">
    提问的"手术刀"：SPIN模型
  </text>
  
  <!-- 漏斗图 -->
  <g transform="translate(960,450)">
    <!-- S层 -->
    <g transform="translate(0,-150)">
      <polygon points="-200,-50 200,-50 -150,0 150,0" fill="#4caf50" opacity="0.8"/>
      <text x="-250" y="-20" font-family="Microsoft YaHei" font-size="48" font-weight="bold" fill="#2e7d32">S</text>
      <text x="0" y="-20" text-anchor="middle" font-family="Microsoft YaHei" font-size="26" font-weight="bold" fill="#ffffff">(Situation) 背景问题</text>

      <rect x="-450" y="20" width="900" height="100" rx="10" fill="#e8f5e8" opacity="0.3"/>
      <text x="-400" y="45" font-family="Microsoft YaHei" font-size="20" font-weight="bold" fill="#2e7d32">目的：</text>
      <text x="-340" y="45" font-family="Microsoft YaHei" font-size="18" fill="#424242">了解现状，建立沟通基础。</text>
      <text x="-400" y="75" font-family="Microsoft YaHei" font-size="20" font-weight="bold" fill="#2e7d32">例：</text>
      <text x="-370" y="75" font-family="Microsoft YaHei" font-size="18" fill="#424242">"王总，目前公司有多少同事？主要分布在哪几个部门？"</text>
    </g>

    <!-- P层 -->
    <g transform="translate(0,0)">
      <polygon points="-150,0 150,0 -100,50 100,50" fill="#2196f3" opacity="0.8"/>
      <text x="-200" y="30" font-family="Microsoft YaHei" font-size="48" font-weight="bold" fill="#1565c0">P</text>
      <text x="0" y="30" text-anchor="middle" font-family="Microsoft YaHei" font-size="26" font-weight="bold" fill="#ffffff">(Problem) 难点问题</text>

      <rect x="-450" y="70" width="900" height="100" rx="10" fill="#e3f2fd" opacity="0.3"/>
      <text x="-400" y="95" font-family="Microsoft YaHei" font-size="20" font-weight="bold" fill="#1565c0">目的：</text>
      <text x="-340" y="95" font-family="Microsoft YaHei" font-size="18" fill="#424242">挖掘痛点，让客户自己说出问题。</text>
      <text x="-400" y="125" font-family="Microsoft YaHei" font-size="20" font-weight="bold" fill="#1565c0">例：</text>
      <text x="-370" y="125" font-family="Microsoft YaHei" font-size="18" fill="#424242">"那大家在跨部门协作的时候，会不会觉得文件传来传去很麻烦？"</text>
    </g>

    <!-- I层 -->
    <g transform="translate(0,150)">
      <polygon points="-100,50 100,50 -50,100 50,100" fill="#ff9800" opacity="0.8"/>
      <text x="-150" y="80" font-family="Microsoft YaHei" font-size="48" font-weight="bold" fill="#f57c00">I</text>
      <text x="0" y="80" text-anchor="middle" font-family="Microsoft YaHei" font-size="26" font-weight="bold" fill="#ffffff">(Implication) 暗示问题</text>

      <rect x="-450" y="120" width="900" height="100" rx="10" fill="#fff3e0" opacity="0.3"/>
      <text x="-400" y="145" font-family="Microsoft YaHei" font-size="20" font-weight="bold" fill="#f57c00">目的：</text>
      <text x="-340" y="145" font-family="Microsoft YaHei" font-size="18" fill="#424242">放大痛点，让客户感受到问题的严重性。</text>
      <text x="-400" y="175" font-family="Microsoft YaHei" font-size="20" font-weight="bold" fill="#f57c00">例：</text>
      <text x="-370" y="175" font-family="Microsoft YaHei" font-size="18" fill="#424242">"如果一份重要的合同，因为版本传错了而耽误了签约，</text>
      <text x="-370" y="195" font-family="Microsoft YaHei" font-size="18" fill="#424242">可能会带来多大的损失？"</text>
    </g>

    <!-- N层 -->
    <g transform="translate(0,300)">
      <polygon points="-50,100 50,100 -25,150 25,150" fill="#9c27b0" opacity="0.8"/>
      <text x="-100" y="130" font-family="Microsoft YaHei" font-size="48" font-weight="bold" fill="#7b1fa2">N</text>
      <text x="0" y="130" text-anchor="middle" font-family="Microsoft YaHei" font-size="24" font-weight="bold" fill="#ffffff">(Need-payoff) 需求-效益问题</text>

      <rect x="-450" y="170" width="900" height="120" rx="10" fill="#f3e5f5" opacity="0.3"/>
      <text x="-400" y="195" font-family="Microsoft YaHei" font-size="20" font-weight="bold" fill="#7b1fa2">目的：</text>
      <text x="-340" y="195" font-family="Microsoft YaHei" font-size="18" fill="#424242">引导价值，让客户自己说出解决方案的好处。</text>
      <text x="-400" y="225" font-family="Microsoft YaHei" font-size="20" font-weight="bold" fill="#7b1fa2">例：</text>
      <text x="-370" y="225" font-family="Microsoft YaHei" font-size="18" fill="#424242">"如果我们有一套方案，能让文件永远保持最新版本，</text>
      <text x="-370" y="245" font-family="Microsoft YaHei" font-size="18" fill="#424242">您觉得这对提升公司的风险管控能力有多大帮助？"</text>
    </g>
  </g>
  
  <!-- 左侧手术刀图标 -->
  <g transform="translate(300,500)">
    <rect x="-5" y="-100" width="10" height="200" fill="#607d8b"/>
    <polygon points="0,-120 -15,-100 15,-100" fill="#37474f"/>
    <circle cx="0" cy="80" r="20" fill="#8d6e63"/>
    <text x="0" y="120" text-anchor="middle" font-family="Microsoft YaHei" font-size="20" fill="#424242">精准切入</text>
  </g>
  
  <!-- 右侧效果说明 -->
  <g transform="translate(1500,400)">
    <rect x="0" y="0" width="300" height="200" rx="15" fill="#e8eaf6" opacity="0.3"/>
    <text x="150" y="30" text-anchor="middle" font-family="Microsoft YaHei" font-size="24" font-weight="bold" fill="#3f51b5">SPIN效果</text>
    
    <circle cx="30" cy="70" r="6" fill="#3f51b5"/>
    <text x="50" y="80" font-family="Microsoft YaHei" font-size="18" fill="#424242">层层递进，步步深入</text>
    
    <circle cx="30" cy="100" r="6" fill="#3f51b5"/>
    <text x="50" y="110" font-family="Microsoft YaHei" font-size="18" fill="#424242">让客户自己说出需求</text>
    
    <circle cx="30" cy="130" r="6" fill="#3f51b5"/>
    <text x="50" y="140" font-family="Microsoft YaHei" font-size="18" fill="#424242">从小问题放大成大问题</text>
    
    <circle cx="30" cy="160" r="6" fill="#3f51b5"/>
    <text x="50" y="170" font-family="Microsoft YaHei" font-size="18" fill="#424242">客户亲口认可解决方案价值</text>
  </g>
  
  <!-- 底部总结 -->
  <g transform="translate(960,900)">
    <rect x="-600" y="-40" width="1200" height="80" rx="15" fill="#e1f5fe" opacity="0.3"/>
    <text x="0" y="-10" text-anchor="middle" font-family="Microsoft YaHei" font-size="32" font-weight="bold" fill="#0277bd">
      SPIN：顶级销售的提问"手术刀"，区分普通销售和顶尖销售的关键技能
    </text>
    <text x="0" y="25" text-anchor="middle" font-family="Microsoft YaHei" font-size="24" fill="#424242">
      四个层次环环相扣，让客户从认识问题到主动寻求解决方案
    </text>
  </g>
</svg>
