<?xml version="1.0" encoding="UTF-8"?>
<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="#ffffff"/>
  
  <!-- 装饰性弧线背景 -->
  <path d="M0,100 Q960,50 1920,100" stroke="#e8f5e8" stroke-width="25" fill="none" opacity="0.3"/>
  
  <!-- 标题 -->
  <text x="960" y="120" text-anchor="middle" font-family="Microsoft YaHei" font-size="48" font-weight="bold" fill="#2e7d32">
    明日任务部署
  </text>
  
  <!-- 跑道背景 -->
  <g transform="translate(960,350)">
    <!-- 跑道 -->
    <rect x="-400" y="-50" width="800" height="100" fill="#607d8b" opacity="0.8"/>
    <rect x="-380" y="-30" width="760" height="60" fill="#90a4ae"/>
    
    <!-- 跑道线 -->
    <g stroke="#ffffff" stroke-width="4">
      <line x1="-350" y1="0" x2="-300" y2="0" stroke-dasharray="20,10"/>
      <line x1="-250" y1="0" x2="-200" y2="0" stroke-dasharray="20,10"/>
      <line x1="-150" y1="0" x2="-100" y2="0" stroke-dasharray="20,10"/>
      <line x1="-50" y1="0" x2="0" y2="0" stroke-dasharray="20,10"/>
      <line x1="50" y1="0" x2="100" y2="0" stroke-dasharray="20,10"/>
      <line x1="150" y1="0" x2="200" y2="0" stroke-dasharray="20,10"/>
      <line x1="250" y1="0" x2="300" y2="0" stroke-dasharray="20,10"/>
    </g>
    
    <!-- 终点线 -->
    <g transform="translate(350,0)">
      <rect x="0" y="-60" width="10" height="120" fill="#f44336"/>
      <rect x="0" y="-60" width="10" height="15" fill="#ffffff"/>
      <rect x="0" y="-30" width="10" height="15" fill="#ffffff"/>
      <rect x="0" y="0" width="10" height="15" fill="#ffffff"/>
      <rect x="0" y="30" width="10" height="15" fill="#ffffff"/>
      
      <!-- 终点旗帜 -->
      <rect x="10" y="-60" width="5" height="80" fill="#8d6e63"/>
      <polygon points="15,-60 60,-45 60,-30 15,-45" fill="#f44336"/>
      <text x="37" y="-35" text-anchor="middle" font-family="Microsoft YaHei" font-size="12" font-weight="bold" fill="#ffffff">终点</text>
    </g>
    
    <!-- 奔跑的人 -->
    <g transform="translate(250,0)">
      <circle cx="0" cy="-30" r="15" fill="#2196f3" opacity="0.8"/>
      <rect x="-12" y="-15" width="24" height="35" fill="#2196f3" opacity="0.8"/>
      <!-- 奔跑姿态 -->
      <rect x="12" y="-5" width="20" height="8" fill="#2196f3" opacity="0.8"/>
      <rect x="-32" y="5" width="20" height="8" fill="#2196f3" opacity="0.8"/>
      <rect x="5" y="30" width="8" height="25" fill="#2196f3" opacity="0.8"/>
      <rect x="-20" y="35" width="8" height="20" fill="#2196f3" opacity="0.8"/>
      
      <!-- 速度线 -->
      <g stroke="#ffeb3b" stroke-width="2" opacity="0.6">
        <line x1="-50" y1="-20" x2="-30" y2="-20"/>
        <line x1="-45" y1="-10" x2="-25" y2="-10"/>
        <line x1="-40" y1="0" x2="-20" y2="0"/>
      </g>
    </g>
  </g>
  
  <!-- 作战代号 -->
  <g transform="translate(300,550)">
    <rect x="0" y="0" width="400" height="100" rx="15" fill="#2e7d32" opacity="0.8"/>
    <text x="200" y="35" text-anchor="middle" font-family="Microsoft YaHei" font-size="24" font-weight="bold" fill="#ffffff">作战代号</text>
    <text x="200" y="70" text-anchor="middle" font-family="Microsoft YaHei" font-size="36" font-weight="bold" fill="#ffffff">临门一脚</text>
  </g>
  
  <!-- 核心任务 -->
  <g transform="translate(960,550)">
    <rect x="-200" y="0" width="400" height="100" rx="15" fill="#f44336" opacity="0.8"/>
    <text x="0" y="35" text-anchor="middle" font-family="Microsoft YaHei" font-size="24" font-weight="bold" fill="#ffffff">核心任务</text>
    <text x="0" y="70" text-anchor="middle" font-family="Microsoft YaHei" font-size="28" font-weight="bold" fill="#ffffff">促成签单，颗粒归仓！</text>
  </g>
  
  <!-- 今晚家庭作业 -->
  <g transform="translate(960,700)">
    <rect x="-600" y="0" width="1200" height="120" rx="15" fill="#e8f5e8" opacity="0.3"/>
    <text x="0" y="35" text-anchor="middle" font-family="Microsoft YaHei" font-size="28" font-weight="bold" fill="#2e7d32">今晚家庭作业</text>
    <text x="0" y="65" text-anchor="middle" font-family="Microsoft YaHei" font-size="22" fill="#424242">针对今日已打出明确意向的客户，仔细复盘其"购买信号"。</text>
    <text x="0" y="95" text-anchor="middle" font-family="Microsoft YaHei" font-size="22" font-weight="bold" fill="#2e7d32">精心设计你明天准备使用的"临门一脚"促成话术。</text>
  </g>
  
  <!-- 明日晨会内容 -->
  <g transform="translate(1420,550)">
    <rect x="-200" y="0" width="400" height="100" rx="15" fill="#ff9800" opacity="0.8"/>
    <text x="0" y="35" text-anchor="middle" font-family="Microsoft YaHei" font-size="24" font-weight="bold" fill="#ffffff">明日晨会内容</text>
    <text x="0" y="70" text-anchor="middle" font-family="Microsoft YaHei" font-size="20" fill="#ffffff">"临门一脚"技巧点拨 &amp; 誓师大会！</text>
  </g>
  
  <!-- 底部金句 -->
  <g transform="translate(960,900)">
    <rect x="-400" y="-40" width="800" height="80" rx="15" fill="#e8f5e8" opacity="0.3"/>
    <text x="0" y="5" text-anchor="middle" font-family="Microsoft YaHei" font-size="36" font-weight="bold" fill="#2e7d32">
      胜利，只差最后一步！
    </text>
  </g>
  
  <!-- 装饰性足球图标 -->
  <g transform="translate(200,600)" opacity="0.4">
    <circle cx="0" cy="0" r="20" fill="#ffffff" stroke="#37474f" stroke-width="2"/>
    <path d="M-10,-10 L10,10 M10,-10 L-10,10" stroke="#37474f" stroke-width="2"/>
    <circle cx="0" cy="0" r="5" fill="#37474f"/>
  </g>
  
  <g transform="translate(1720,600)" opacity="0.4">
    <circle cx="0" cy="0" r="20" fill="#ffffff" stroke="#37474f" stroke-width="2"/>
    <path d="M-10,-10 L10,10 M10,-10 L-10,10" stroke="#37474f" stroke-width="2"/>
    <circle cx="0" cy="0" r="5" fill="#37474f"/>
  </g>
</svg>
