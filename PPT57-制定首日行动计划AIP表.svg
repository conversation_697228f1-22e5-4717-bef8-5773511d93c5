<?xml version="1.0" encoding="UTF-8"?>
<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="#ffffff"/>
  
  <!-- 装饰性弧线背景 -->
  <path d="M0,100 Q960,50 1920,100" stroke="#e8f5e8" stroke-width="25" fill="none" opacity="0.3"/>
  
  <!-- 标题 -->
  <text x="960" y="120" text-anchor="middle" font-family="Microsoft YaHei" font-size="56" font-weight="bold" fill="#2e7d32">
    制定《首日行动计划 (AIP) 表》
  </text>
  
  <!-- AIP表格 -->
  <g transform="translate(200,250)">
    <rect x="0" y="0" width="1520" height="500" rx="20" fill="#e8f5e8" opacity="0.2" stroke="#2e7d32" stroke-width="3"/>
    
    <!-- 表头 -->
    <rect x="0" y="0" width="1520" height="80" fill="#2e7d32" opacity="0.8"/>
    
    <!-- 表头文字 -->
    <text x="250" y="50" text-anchor="middle" font-family="Microsoft YaHei" font-size="24" font-weight="bold" fill="#ffffff">A (Action) 行动</text>
    <text x="760" y="50" text-anchor="middle" font-family="Microsoft YaHei" font-size="24" font-weight="bold" fill="#ffffff">I (Impact) 预期影响/成果</text>
    <text x="1270" y="50" text-anchor="middle" font-family="Microsoft YaHei" font-size="24" font-weight="bold" fill="#ffffff">P (Pillar) 所需支撑/支柱</text>
    
    <!-- 表格分割线 -->
    <line x1="500" y1="0" x2="500" y2="500" stroke="#2e7d32" stroke-width="2"/>
    <line x1="1020" y1="0" x2="1020" y2="500" stroke="#2e7d32" stroke-width="2"/>
    <line x1="0" y1="80" x2="1520" y2="80" stroke="#2e7d32" stroke-width="2"/>
    
    <!-- 示例内容 -->
    <g transform="translate(0,100)">
      <!-- A列内容 -->
      <text x="50" y="30" font-family="Microsoft YaHei" font-size="18" font-weight="bold" fill="#2e7d32">(具体、可执行的动作)</text>
      <text x="50" y="60" font-family="Microsoft YaHei" font-size="16" fill="#d32f2f">错误：联系客户</text>
      <text x="50" y="90" font-family="Microsoft YaHei" font-size="16" font-weight="bold" fill="#4caf50">正确：上午9:30，由小王致电</text>
      <text x="50" y="115" font-family="Microsoft YaHei" font-size="16" font-weight="bold" fill="#4caf50">XX公司张经理，预约下午拜访。</text>
      
      <!-- I列内容 -->
      <text x="550" y="30" font-family="Microsoft YaHei" font-size="18" font-weight="bold" fill="#1565c0">(你想要达成的目的)</text>
      <text x="550" y="60" font-family="Microsoft YaHei" font-size="16" font-weight="bold" fill="#1565c0">示例：</text>
      <text x="550" y="85" font-family="Microsoft YaHei" font-size="16" fill="#424242">成功预约到明天下午3点的半小时</text>
      <text x="550" y="110" font-family="Microsoft YaHei" font-size="16" fill="#424242">会面机会，并初步探询其对员工</text>
      <text x="550" y="135" font-family="Microsoft YaHei" font-size="16" fill="#424242">福利方案的看法。</text>
      
      <!-- P列内容 -->
      <text x="1070" y="30" font-family="Microsoft YaHei" font-size="18" font-weight="bold" fill="#f57c00">(你需要什么帮助)</text>
      <text x="1070" y="60" font-family="Microsoft YaHei" font-size="16" font-weight="bold" fill="#f57c00">示例：</text>
      <text x="1070" y="85" font-family="Microsoft YaHei" font-size="16" fill="#424242">需要市场部提供最新的"弹性福利</text>
      <text x="1070" y="110" font-family="Microsoft YaHei" font-size="16" fill="#424242">平台"介绍彩页电子版。</text>
    </g>
  </g>
  
  <!-- 底部要求 -->
  <g transform="translate(960,850)">
    <rect x="-700" y="-50" width="1400" height="100" rx="15" fill="#fff3e0" opacity="0.3"/>
    <text x="0" y="-10" text-anchor="middle" font-family="Microsoft YaHei" font-size="28" font-weight="bold" fill="#f57c00">
      针对你们选定的首要目标客户，用10分钟时间，完成这份计划的填写
    </text>
    <text x="0" y="25" text-anchor="middle" font-family="Microsoft YaHei" font-size="24" fill="#424242">
      'A-行动'要具体到谁、在什么时间、做什么事；'I-预期影响'要清晰；'P-所需支撑'要明确
    </text>
  </g>
  
  <!-- 装饰性计划图标 -->
  <g transform="translate(150,400)" opacity="0.4">
    <rect x="0" y="0" width="40" height="30" fill="#2e7d32"/>
    <rect x="5" y="5" width="30" height="20" fill="#ffffff"/>
    <line x1="10" y1="10" x2="30" y2="10" stroke="#2e7d32" stroke-width="1"/>
    <line x1="10" y1="15" x2="25" y2="15" stroke="#2e7d32" stroke-width="1"/>
    <line x1="10" y1="20" x2="28" y2="20" stroke="#2e7d32" stroke-width="1"/>
  </g>
</svg>
