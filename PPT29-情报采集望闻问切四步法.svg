<?xml version="1.0" encoding="UTF-8"?>
<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="#ffffff"/>
  
  <!-- 装饰性弧线背景 -->
  <path d="M0,100 Q960,50 1920,100" stroke="#e8f5e8" stroke-width="25" fill="none" opacity="0.3"/>
  
  <!-- 标题 -->
  <text x="960" y="120" text-anchor="middle" font-family="Microsoft YaHei" font-size="56" font-weight="bold" fill="#2e7d32">
    情报采集："望闻问切"四步法
  </text>
  
  <!-- 望 (公开渠道检索) -->
  <g transform="translate(480,250)">
    <rect x="0" y="0" width="400" height="180" rx="15" fill="#e8f5e8" opacity="0.3"/>

    <!-- 望诊图标 -->
    <g transform="translate(80,90)">
      <circle cx="0" cy="0" r="30" fill="#4caf50" opacity="0.8"/>
      <circle cx="-8" cy="-8" r="6" fill="#ffffff"/>
      <circle cx="8" cy="-8" r="6" fill="#ffffff"/>
      <circle cx="-8" cy="-8" r="3" fill="#2e7d32"/>
      <circle cx="8" cy="-8" r="3" fill="#2e7d32"/>
      <path d="M-10,8 Q0,15 10,8" stroke="#ffffff" stroke-width="3" fill="none"/>
    </g>

    <text x="200" y="40" text-anchor="middle" font-family="Microsoft YaHei" font-size="28" font-weight="bold" fill="#2e7d32">望 (公开渠道检索)</text>
    <text x="200" y="80" text-anchor="middle" font-family="Microsoft YaHei" font-size="22" fill="#424242">官网、财报、新闻、企查查…</text>
    <text x="200" y="105" text-anchor="middle" font-family="Microsoft YaHei" font-size="22" fill="#424242">(看其表)</text>
  </g>

  <!-- 闻 (侧面信息打探) -->
  <g transform="translate(1040,250)">
    <rect x="0" y="0" width="400" height="180" rx="15" fill="#e3f2fd" opacity="0.3"/>

    <!-- 闻诊图标 -->
    <g transform="translate(80,90)">
      <circle cx="0" cy="0" r="30" fill="#2196f3" opacity="0.8"/>
      <ellipse cx="-5" cy="0" rx="8" ry="12" fill="#ffffff"/>
      <ellipse cx="5" cy="0" rx="8" ry="12" fill="#ffffff"/>
      <circle cx="-5" cy="0" r="4" fill="#1976d2"/>
      <circle cx="5" cy="0" r="4" fill="#1976d2"/>
      <!-- 声波 -->
      <path d="M25,0 Q35,-10 45,0 Q35,10 25,0" stroke="#ffffff" stroke-width="2" fill="none"/>
    </g>

    <text x="200" y="40" text-anchor="middle" font-family="Microsoft YaHei" font-size="28" font-weight="bold" fill="#1565c0">闻 (侧面信息打探)</text>
    <text x="200" y="80" text-anchor="middle" font-family="Microsoft YaHei" font-size="22" fill="#424242">供应商、合作伙伴、离职员工…</text>
    <text x="200" y="105" text-anchor="middle" font-family="Microsoft YaHei" font-size="22" fill="#424242">(听其言)</text>
  </g>

  <!-- 问 (核心人物访谈) -->
  <g transform="translate(480,480)">
    <rect x="0" y="0" width="400" height="180" rx="15" fill="#fff3e0" opacity="0.3"/>

    <!-- 问诊图标 -->
    <g transform="translate(80,90)">
      <circle cx="0" cy="0" r="30" fill="#ff9800" opacity="0.8"/>
      <circle cx="0" cy="-10" r="8" fill="#ffffff"/>
      <rect x="-6" y="2" width="12" height="16" fill="#ffffff"/>
      <!-- 问号 -->
      <text x="0" y="12" text-anchor="middle" font-family="Microsoft YaHei" font-size="16" fill="#f57c00">?</text>
    </g>

    <text x="200" y="40" text-anchor="middle" font-family="Microsoft YaHei" font-size="28" font-weight="bold" fill="#f57c00">问 (核心人物访谈)</text>
    <text x="200" y="80" text-anchor="middle" font-family="Microsoft YaHei" font-size="22" fill="#424242">运用SPIN提问模型，深挖需求</text>
    <text x="200" y="105" text-anchor="middle" font-family="Microsoft YaHei" font-size="22" fill="#424242">(问其症)</text>
  </g>

  <!-- 切 (现场实地勘察) -->
  <g transform="translate(1040,480)">
    <rect x="0" y="0" width="400" height="180" rx="15" fill="#fce4ec" opacity="0.3"/>

    <!-- 切诊图标 -->
    <g transform="translate(80,90)">
      <circle cx="0" cy="0" r="30" fill="#e91e63" opacity="0.8"/>
      <circle cx="0" cy="-10" r="8" fill="#ffffff"/>
      <rect x="-6" y="2" width="12" height="16" fill="#ffffff"/>
      <!-- 手指 -->
      <ellipse cx="0" cy="8" rx="4" ry="8" fill="#c2185b"/>
      <circle cx="0" cy="0" r="3" fill="#c2185b"/>
    </g>

    <text x="200" y="40" text-anchor="middle" font-family="Microsoft YaHei" font-size="28" font-weight="bold" fill="#c2185b">切 (现场实地勘察)</text>
    <text x="200" y="80" text-anchor="middle" font-family="Microsoft YaHei" font-size="22" fill="#424242">深入办公室、车间，感受"体感"</text>
    <text x="200" y="105" text-anchor="middle" font-family="Microsoft YaHei" font-size="22" fill="#424242">(触其里)</text>
  </g>
  
  <!-- 中医诊断流程箭头 -->
  <path d="M880,375 L1040,375" stroke="#4caf50" stroke-width="4" marker-end="url(#greenArrow)"/>
  <path d="M680,450 L680,500" stroke="#2196f3" stroke-width="4" marker-end="url(#blueArrow)"/>
  <path d="M880,575 L1040,575" stroke="#ff9800" stroke-width="4" marker-end="url(#orangeArrow)"/>
  
  <!-- 底部总结 -->
  <g transform="translate(960,750)">
    <rect x="-500" y="-50" width="1000" height="100" rx="15" fill="#f3e5f5" opacity="0.3"/>
    <text x="0" y="-10" text-anchor="middle" font-family="Microsoft YaHei" font-size="32" font-weight="bold" fill="#7b1fa2">
      中医诊断智慧：四步法环环相扣，层层深入
    </text>
    <text x="0" y="25" text-anchor="middle" font-family="Microsoft YaHei" font-size="24" fill="#424242">
      从表象到本质，从外部到内部，全方位收集客户情报
    </text>
  </g>
  
  <!-- 装饰性中医元素 -->
  <g transform="translate(200,400)" opacity="0.3">
    <circle cx="0" cy="0" r="40" fill="none" stroke="#2e7d32" stroke-width="2"/>
    <path d="M-20,-20 Q0,0 20,-20 Q0,0 -20,20 Q0,0 20,20 Q0,0 -20,-20" fill="#2e7d32"/>
    <circle cx="0" cy="-10" r="8" fill="#ffffff"/>
    <circle cx="0" cy="10" r="8" fill="#2e7d32"/>
  </g>
  
  <g transform="translate(1720,400)" opacity="0.3">
    <circle cx="0" cy="0" r="40" fill="none" stroke="#2e7d32" stroke-width="2"/>
    <path d="M-20,-20 Q0,0 20,-20 Q0,0 -20,20 Q0,0 20,20 Q0,0 -20,-20" fill="#2e7d32"/>
    <circle cx="0" cy="-10" r="8" fill="#ffffff"/>
    <circle cx="0" cy="10" r="8" fill="#2e7d32"/>
  </g>
  
  <!-- 箭头标记定义 -->
  <defs>
    <marker id="greenArrow" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#4caf50"/>
    </marker>
    <marker id="blueArrow" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#2196f3"/>
    </marker>
    <marker id="orangeArrow" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#ff9800"/>
    </marker>
  </defs>
</svg>
