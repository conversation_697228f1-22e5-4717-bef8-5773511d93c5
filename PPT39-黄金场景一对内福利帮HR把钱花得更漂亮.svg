<?xml version="1.0" encoding="UTF-8"?>
<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="#ffffff"/>
  
  <!-- 装饰性弧线背景 -->
  <path d="M0,100 Q960,50 1920,100" stroke="#e8f5e8" stroke-width="25" fill="none" opacity="0.3"/>
  
  <!-- 标题 -->
  <text x="960" y="120" text-anchor="middle" font-family="Microsoft YaHei" font-size="48" font-weight="bold" fill="#2e7d32">
    黄金场景一：对内福利（帮HR把钱花得更漂亮）
  </text>
  
  <!-- 打法一：常规福利"数智化"升级 -->
  <g transform="translate(200,250)">
    <rect x="0" y="0" width="700" height="300" rx="20" fill="#e8f5e8" opacity="0.3"/>
    <text x="350" y="40" text-anchor="middle" font-family="Microsoft YaHei" font-size="32" font-weight="bold" fill="#2e7d32">打法一：常规福利"数智化"升级</text>
    
    <g transform="translate(100,100)">
      <text x="0" y="0" font-family="Microsoft YaHei" font-size="24" font-weight="bold" fill="#2e7d32">通信补贴</text>
      <path d="M120,0 L200,0" stroke="#4caf50" stroke-width="4" marker-end="url(#greenArrow)"/>
      <text x="220" y="0" font-family="Microsoft YaHei" font-size="24" font-weight="bold" fill="#2e7d32">"企业统付、员工选餐"平台</text>
    </g>
    
    <g transform="translate(100,150)">
      <text x="0" y="0" font-family="Microsoft YaHei" font-size="24" font-weight="bold" fill="#2e7d32">节日礼包</text>
      <path d="M120,0 L200,0" stroke="#4caf50" stroke-width="4" marker-end="url(#greenArrow)"/>
      <text x="220" y="0" font-family="Microsoft YaHei" font-size="24" font-weight="bold" fill="#2e7d32">"实物+数字权益"组合礼包</text>
    </g>
  </g>
  
  <!-- 打法二：创新福利"平台化"运营 -->
  <g transform="translate(200,600)">
    <rect x="0" y="0" width="1200" height="350" rx="20" fill="#e3f2fd" opacity="0.3"/>
    <text x="600" y="40" text-anchor="middle" font-family="Microsoft YaHei" font-size="32" font-weight="bold" fill="#1565c0">打法二：创新福利"平台化"运营</text>
    
    <text x="50" y="90" font-family="Microsoft YaHei" font-size="24" font-weight="bold" fill="#1565c0">核心痛点：</text>
    <text x="180" y="90" font-family="Microsoft YaHei" font-size="22" fill="#424242">传统福利众口难调</text>
    
    <text x="50" y="130" font-family="Microsoft YaHei" font-size="24" font-weight="bold" fill="#1565c0">解决方案：</text>
    <text x="180" y="130" font-family="Microsoft YaHei" font-size="22" fill="#424242">共建"弹性福利积分商城"</text>
    
    <circle cx="100" cy="170" r="6" fill="#2196f3"/>
    <text x="120" y="180" font-family="Microsoft YaHei" font-size="20" fill="#424242">企业发积分，员工自由兑换</text>
    
    <circle cx="100" cy="210" r="6" fill="#2196f3"/>
    <text x="120" y="220" font-family="Microsoft YaHei" font-size="20" fill="#424242">我们的价值：提供海量的、独特的"数字商品"和"权益商品"</text>
    <text x="120" y="245" font-family="Microsoft YaHei" font-size="20" fill="#424242">（话费/流量/会员/购机券…）</text>
  </g>
  
  <!-- 右下角话术示范 -->
  <g transform="translate(1450,600)">
    <rect x="0" y="0" width="400" height="350" rx="20" fill="#fff3e0" opacity="0.3"/>
    <text x="200" y="40" text-anchor="middle" font-family="Microsoft YaHei" font-size="24" font-weight="bold" fill="#f57c00">话术示范 (对HR总监)</text>
    
    <text x="50" y="80" font-family="Microsoft YaHei" font-size="18" fill="#424242">"王总，我理解您每年做福利</text>
    <text x="50" y="105" font-family="Microsoft YaHei" font-size="18" fill="#424242">方案都挺头疼的…</text>
    
    <text x="50" y="140" font-family="Microsoft YaHei" font-size="18" fill="#424242">我们现在有个新模式，叫</text>
    <text x="50" y="165" font-family="Microsoft YaHei" font-size="18" fill="#424242">'弹性福利积分平台'…</text>
    
    <text x="50" y="200" font-family="Microsoft YaHei" font-size="18" fill="#424242">您省心了，员工满意度</text>
    <text x="50" y="225" font-family="Microsoft YaHei" font-size="18" fill="#424242">也高了…"</text>
    
    <!-- 对话框尾巴 -->
    <polygon points="0,150 -20,160 0,170" fill="#fff3e0" opacity="0.3"/>
  </g>
  
  <!-- 装饰性HR图标 -->
  <g transform="translate(1000,300)" opacity="0.4">
    <circle cx="0" cy="0" r="40" fill="#4caf50"/>
    <circle cx="0" cy="-10" r="15" fill="#ffffff"/>
    <rect x="-20" y="10" width="40" height="30" fill="#ffffff"/>
    <text x="0" y="30" text-anchor="middle" font-family="Microsoft YaHei" font-size="12" fill="#4caf50">HR</text>
  </g>
  
  <!-- 箭头标记定义 -->
  <defs>
    <marker id="greenArrow" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#4caf50"/>
    </marker>
  </defs>
</svg>
