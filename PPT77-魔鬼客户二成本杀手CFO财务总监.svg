<?xml version="1.0" encoding="UTF-8"?>
<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="#ffffff"/>
  
  <!-- 装饰性弧线背景 -->
  <path d="M0,100 Q960,50 1920,100" stroke="#fff3e0" stroke-width="25" fill="none" opacity="0.3"/>
  
  <!-- 标题 -->
  <text x="960" y="120" text-anchor="middle" font-family="Microsoft YaHei" font-size="48" font-weight="bold" fill="#f57c00">
    魔鬼客户二：成本杀手 (CFO/财务总监)
  </text>
  
  <!-- 天平和算盘图标 -->
  <g transform="translate(960,300)">
    <!-- 天平 -->
    <g transform="translate(-50,0)">
      <!-- 天平支架 -->
      <rect x="-3" y="-50" width="6" height="100" fill="#8d6e63"/>
      <rect x="-40" y="-55" width="80" height="6" fill="#8d6e63"/>
      
      <!-- 左盘 -->
      <ellipse cx="-35" cy="-45" rx="25" ry="8" fill="#ff9800" opacity="0.8"/>
      <rect x="-60" y="-50" width="50" height="5" fill="#ff9800"/>
      
      <!-- 右盘 -->
      <ellipse cx="35" cy="-35" rx="25" ry="8" fill="#ff9800" opacity="0.8"/>
      <rect x="10" y="-40" width="50" height="5" fill="#ff9800"/>
      
      <!-- 连接链 -->
      <line x1="-35" y1="-50" x2="-35" y2="-55" stroke="#8d6e63" stroke-width="2"/>
      <line x1="35" y1="-40" x2="35" y2="-55" stroke="#8d6e63" stroke-width="2"/>
      
      <!-- 重量显示 -->
      <circle cx="-35" cy="-35" r="8" fill="#f44336"/>
      <text x="-35" y="-30" text-anchor="middle" font-family="Microsoft YaHei" font-size="10" font-weight="bold" fill="#ffffff">$</text>
      
      <circle cx="35" cy="-25" r="8" fill="#4caf50"/>
      <text x="35" y="-20" text-anchor="middle" font-family="Microsoft YaHei" font-size="10" font-weight="bold" fill="#ffffff">ROI</text>
    </g>
    
    <!-- 算盘 -->
    <g transform="translate(50,0)">
      <rect x="-30" y="-40" width="60" height="80" rx="5" fill="#8d6e63"/>
      <rect x="-25" y="-35" width="50" height="70" fill="#a1887f"/>
      
      <!-- 算盘珠子 -->
      <g fill="#d32f2f">
        <circle cx="-15" cy="-20" r="4"/>
        <circle cx="-5" cy="-20" r="4"/>
        <circle cx="5" cy="-20" r="4"/>
        <circle cx="15" cy="-20" r="4"/>
        
        <circle cx="-15" cy="0" r="4"/>
        <circle cx="-5" cy="0" r="4"/>
        <circle cx="5" cy="0" r="4"/>
        <circle cx="15" cy="0" r="4"/>
        
        <circle cx="-15" cy="20" r="4"/>
        <circle cx="-5" cy="20" r="4"/>
        <circle cx="5" cy="20" r="4"/>
        <circle cx="15" cy="20" r="4"/>
      </g>
      
      <!-- 算盘横梁 -->
      <rect x="-25" y="-30" width="50" height="3" fill="#5d4037"/>
      <rect x="-25" y="-10" width="50" height="3" fill="#5d4037"/>
      <rect x="-25" y="10" width="50" height="3" fill="#5d4037"/>
      <rect x="-25" y="30" width="50" height="3" fill="#5d4037"/>
    </g>
  </g>
  
  <!-- 口头禅 -->
  <g transform="translate(960,450)">
    <rect x="-400" y="-40" width="800" height="80" rx="15" fill="#fff3e0" opacity="0.3"/>
    <text x="0" y="-10" text-anchor="middle" font-family="Microsoft YaHei" font-size="24" font-weight="bold" fill="#f57c00">他的口头禅：</text>
    <text x="0" y="25" text-anchor="middle" font-family="Microsoft YaHei" font-size="28" font-weight="bold" fill="#d32f2f">"其他的我不管，我就想知道多少钱。"</text>
  </g>
  
  <!-- 他关心的问题 -->
  <g transform="translate(960,650)">
    <rect x="-700" y="-150" width="1400" height="300" rx="20" fill="#fff3e0" opacity="0.3"/>
    <text x="0" y="-110" text-anchor="middle" font-family="Microsoft YaHei" font-size="32" font-weight="bold" fill="#f57c00">他关心的问题</text>
    
    <g transform="translate(-500,-50)">
      <circle cx="0" cy="0" r="6" fill="#f57c00"/>
      <text x="20" y="5" font-family="Microsoft YaHei" font-size="18" fill="#424242">"不用说那么多功能，我就想知道，用了你们这个，我们现在每年100万的通信开支，能降到多少？"</text>
    </g>
    
    <g transform="translate(-500,0)">
      <circle cx="0" cy="0" r="6" fill="#f57c00"/>
      <text x="20" y="-10" font-family="Microsoft YaHei" font-size="18" fill="#424242">"如果不能降，反而要增加投入，那么新增的这笔投入，我的ROI（投资回报率）是多少？</text>
      <text x="20" y="15" font-family="Microsoft YaHei" font-size="18" fill="#424242">多长时间能看到明确的财务回报？"</text>
    </g>
    
    <g transform="translate(-500,60)">
      <circle cx="0" cy="0" r="6" fill="#f57c00"/>
      <text x="20" y="-10" font-family="Microsoft YaHei" font-size="18" fill="#424242">"这个回报，你是写在合同里，作为对赌条款的吗？</text>
      <text x="20" y="15" font-family="Microsoft YaHei" font-size="18" font-weight="bold" fill="#d32f2f">如果不能，那不就是'画饼'吗？"</text>
    </g>
  </g>
  
  <!-- 装饰性金钱图标 -->
  <g transform="translate(200,400)" opacity="0.4">
    <circle cx="0" cy="0" r="20" fill="#f57c00"/>
    <text x="0" y="8" text-anchor="middle" font-family="Microsoft YaHei" font-size="24" font-weight="bold" fill="#ffffff">¥</text>
  </g>
  
  <g transform="translate(1720,400)" opacity="0.4">
    <circle cx="0" cy="0" r="20" fill="#f57c00"/>
    <text x="0" y="8" text-anchor="middle" font-family="Microsoft YaHei" font-size="24" font-weight="bold" fill="#ffffff">$</text>
  </g>
</svg>
