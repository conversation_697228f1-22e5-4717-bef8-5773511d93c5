<?xml version="1.0" encoding="UTF-8"?>
<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="#ffffff"/>
  
  <!-- 装饰性弧线背景 -->
  <path d="M0,100 Q960,50 1920,100" stroke="#fff3e0" stroke-width="25" fill="none" opacity="0.3"/>
  
  <!-- 标题 -->
  <text x="960" y="120" text-anchor="middle" font-family="Microsoft YaHei" font-size="56" font-weight="bold" fill="#f57c00">
    荣誉之战："龙虎榜"积分竞赛机制
  </text>
  
  <!-- 中央金色奖杯 -->
  <g transform="translate(960,300)">
    <!-- 奖杯主体 -->
    <ellipse cx="0" cy="-30" rx="60" ry="40" fill="#ffd700"/>
    <rect x="-50" y="-30" width="100" height="80" fill="#ffd700"/>
    <ellipse cx="0" cy="50" rx="50" ry="20" fill="#ffb300"/>
    
    <!-- 奖杯底座 -->
    <rect x="-40" y="50" width="80" height="30" fill="#ff9800"/>
    <rect x="-30" y="80" width="60" height="20" fill="#ff8f00"/>
    
    <!-- 奖杯把手 -->
    <ellipse cx="-70" cy="10" rx="20" ry="30" fill="none" stroke="#ffd700" stroke-width="8"/>
    <ellipse cx="70" cy="10" rx="20" ry="30" fill="none" stroke="#ffd700" stroke-width="8"/>
    
    <!-- 光芒效果 -->
    <g opacity="0.8">
      <path d="M0,-100 L8,-70 L0,-40 L-8,-70 Z" fill="#ffeb3b"/>
      <path d="M70,-50 L85,-35 L55,-25 L70,-50 Z" fill="#ffeb3b"/>
      <path d="M-70,-50 L-55,-35 L-85,-25 L-70,-50 Z" fill="#ffeb3b"/>
      <path d="M90,10 L105,20 L80,30 L90,10 Z" fill="#ffeb3b"/>
      <path d="M-90,10 L-80,20 L-105,30 L-90,10 Z" fill="#ffeb3b"/>
    </g>
  </g>
  
  <!-- 左侧：积分获取规则 -->
  <g transform="translate(200,450)">
    <rect x="0" y="0" width="600" height="450" rx="20" fill="#e8f5e8" opacity="0.3"/>
    <text x="300" y="40" text-anchor="middle" font-family="Microsoft YaHei" font-size="32" font-weight="bold" fill="#2e7d32">积分获取规则</text>
    
    <!-- 基础任务分 -->
    <text x="50" y="80" font-family="Microsoft YaHei" font-size="26" font-weight="bold" fill="#2e7d32">基础任务分 (苦劳分)</text>
    
    <circle cx="80" cy="110" r="6" fill="#4caf50"/>
    <text x="100" y="120" font-family="Microsoft YaHei" font-size="20" fill="#424242">有效拜访：+10分</text>
    
    <circle cx="80" cy="140" r="6" fill="#4caf50"/>
    <text x="100" y="150" font-family="Microsoft YaHei" font-size="20" fill="#424242">作战地图：+30分</text>
    
    <circle cx="80" cy="170" r="6" fill="#4caf50"/>
    <text x="100" y="180" font-family="Microsoft YaHei" font-size="20" fill="#424242">方案提报：+50分</text>
    
    <!-- 成果加分 -->
    <text x="50" y="220" font-family="Microsoft YaHei" font-size="26" font-weight="bold" fill="#1565c0">成果加分 (功劳分)</text>
    
    <circle cx="80" cy="250" r="6" fill="#2196f3"/>
    <text x="100" y="260" font-family="Microsoft YaHei" font-size="20" fill="#424242">新增成员：+5分/名</text>
    
    <circle cx="80" cy="280" r="6" fill="#2196f3"/>
    <text x="100" y="290" font-family="Microsoft YaHei" font-size="20" fill="#424242">团购签约：+100-200分</text>
    <text x="100" y="315" font-family="Microsoft YaHei" font-size="18" font-weight="bold" fill="#f44336">(超5万总分翻倍！)</text>
    
    <!-- 创新加分 -->
    <text x="50" y="355" font-family="Microsoft YaHei" font-size="26" font-weight="bold" fill="#7b1fa2">创新加分 (智慧分)</text>
    
    <circle cx="80" cy="385" r="6" fill="#9c27b0"/>
    <text x="100" y="395" font-family="Microsoft YaHei" font-size="20" fill="#424242">最佳实践：+50分</text>
    
    <circle cx="80" cy="415" r="6" fill="#9c27b0"/>
    <text x="100" y="425" font-family="Microsoft YaHei" font-size="20" fill="#424242">发展内线：+20分</text>
  </g>
  
  <!-- 右侧：奖项设置 -->
  <g transform="translate(1120,450)">
    <rect x="0" y="0" width="600" height="450" rx="20" fill="#e3f2fd" opacity="0.3"/>
    <text x="300" y="40" text-anchor="middle" font-family="Microsoft YaHei" font-size="32" font-weight="bold" fill="#1565c0">奖项设置</text>
    
    <!-- 团队大奖 -->
    <text x="50" y="80" font-family="Microsoft YaHei" font-size="26" font-weight="bold" fill="#1565c0">团队大奖</text>
    
    <!-- 冠军奖杯图标 -->
    <g transform="translate(100,120)">
      <ellipse cx="0" cy="-10" rx="25" ry="15" fill="#ffd700"/>
      <rect x="-20" y="-10" width="40" height="30" fill="#ffd700"/>
      <rect x="-15" y="20" width="30" height="15" fill="#ff9800"/>
      <text x="0" y="5" text-anchor="middle" font-family="Microsoft YaHei" font-size="12" font-weight="bold" fill="#ffffff">1</text>
    </g>
    
    <text x="150" y="120" font-family="Microsoft YaHei" font-size="22" font-weight="bold" fill="#f57c00">"业绩先锋"团队 (冠军)</text>
    
    <!-- 协同奖图标 -->
    <g transform="translate(100,170)">
      <circle cx="-10" cy="0" r="12" fill="#2196f3"/>
      <circle cx="10" cy="0" r="12" fill="#2196f3"/>
      <circle cx="0" cy="-15" r="12" fill="#2196f3"/>
      <path d="M-10,0 L10,0" stroke="#ffffff" stroke-width="2"/>
      <path d="M0,-15 L0,0" stroke="#ffffff" stroke-width="2"/>
    </g>
    
    <text x="150" y="175" font-family="Microsoft YaHei" font-size="22" font-weight="bold" fill="#2196f3">"最佳协同"团队</text>
    
    <!-- 个人大奖 -->
    <text x="50" y="230" font-family="Microsoft YaHei" font-size="26" font-weight="bold" fill="#e91e63">个人大奖</text>
    
    <!-- MVP图标 -->
    <g transform="translate(100,270)">
      <polygon points="0,-20 -15,10 15,10" fill="#f44336"/>
      <circle cx="0" cy="0" r="15" fill="#ffd700"/>
      <text x="0" y="5" text-anchor="middle" font-family="Microsoft YaHei" font-size="10" font-weight="bold" fill="#ffffff">MVP</text>
    </g>
    
    <text x="150" y="275" font-family="Microsoft YaHei" font-size="22" font-weight="bold" fill="#e91e63">"金牌客户经理" MVP</text>
    
    <!-- 奖品展示区域 -->
    <rect x="50" y="320" width="500" height="100" rx="10" fill="#fff3e0" opacity="0.5"/>
    <text x="300" y="345" text-anchor="middle" font-family="Microsoft YaHei" font-size="20" font-weight="bold" fill="#f57c00">丰厚奖品等你来拿！</text>
    <text x="300" y="375" text-anchor="middle" font-family="Microsoft YaHei" font-size="18" fill="#424242">冠军奖杯 + 现金奖励 + 荣誉证书</text>
    <text x="300" y="400" text-anchor="middle" font-family="Microsoft YaHei" font-size="18" fill="#424242">个人专属大奖 + 公司表彰</text>
  </g>
  
  <!-- 底部战斗口号 -->
  <g transform="translate(960,950)">
    <rect x="-400" y="-40" width="800" height="80" rx="15" fill="#ffebee" opacity="0.3"/>
    <text x="0" y="-5" text-anchor="middle" font-family="Microsoft YaHei" font-size="36" font-weight="bold" fill="#d32f2f">
      为荣誉而战！为团队而战！
    </text>
  </g>
</svg>
