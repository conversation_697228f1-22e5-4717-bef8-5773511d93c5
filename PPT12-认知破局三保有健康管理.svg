<?xml version="1.0" encoding="UTF-8"?>
<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="#ffffff"/>
  
  <!-- 装饰性弧线背景 -->
  <path d="M0,100 Q960,50 1920,100" stroke="#ffebee" stroke-width="25" fill="none" opacity="0.3"/>
  
  <!-- 标题 -->
  <text x="960" y="120" text-anchor="middle" font-family="Microsoft YaHei" font-size="56" font-weight="bold" fill="#d32f2f">
    认知破局三：保有，不是被动的救火，而是主动的健康管理
  </text>
  
  <!-- 左侧：错误的保有观 -->
  <g transform="translate(200,250)">
    <!-- 红色叉号 -->
    <circle cx="350" cy="100" r="50" fill="#f44336" opacity="0.2"/>
    <line x1="320" y1="70" x2="380" y2="130" stroke="#f44336" stroke-width="6"/>
    <line x1="380" y1="70" x2="320" y2="130" stroke="#f44336" stroke-width="6"/>
    
    <!-- 消防员救火图片区域 -->
    <rect x="0" y="0" width="600" height="200" rx="15" fill="#ffebee" opacity="0.3"/>
    
    <!-- 消防员简化图标 -->
    <g transform="translate(150,100)">
      <circle cx="0" cy="-20" r="15" fill="#f44336"/>
      <rect x="-10" y="-5" width="20" height="30" fill="#f44336"/>
      <rect x="-15" y="5" width="30" height="15" fill="#ff5722"/>
      <!-- 水管 -->
      <line x1="15" y1="10" x2="40" y2="0" stroke="#2196f3" stroke-width="4"/>
      <!-- 火焰 -->
      <g transform="translate(50,-10)">
        <path d="M0,0 Q-5,-10 0,-20 Q5,-15 10,-20 Q15,-10 10,0" fill="#ff5722" opacity="0.7"/>
      </g>
    </g>
    
    <text x="300" y="250" text-anchor="middle" font-family="Microsoft YaHei" font-size="36" font-weight="bold" fill="#d32f2f">错误的保有观</text>
    
    <!-- 特点列表 -->
    <circle cx="50" cy="300" r="6" fill="#f44336"/>
    <text x="80" y="310" font-family="Microsoft YaHei" font-size="28" fill="#424242">事后响应</text>
    
    <circle cx="50" cy="340" r="6" fill="#f44336"/>
    <text x="80" y="350" font-family="Microsoft YaHei" font-size="28" fill="#424242">依赖关系</text>
    
    <circle cx="50" cy="380" r="6" fill="#f44336"/>
    <text x="80" y="390" font-family="Microsoft YaHei" font-size="28" fill="#424242">指标驱动（挽留率）</text>
  </g>
  
  <!-- 右侧：正确的保有观 -->
  <g transform="translate(1120,250)">
    <!-- 绿色勾号 -->
    <circle cx="350" cy="100" r="50" fill="#4caf50" opacity="0.2"/>
    <path d="M320,100 L340,120 L380,80" stroke="#4caf50" stroke-width="6" fill="none"/>
    
    <!-- 医生体检图片区域 -->
    <rect x="0" y="0" width="600" height="200" rx="15" fill="#e8f5e8" opacity="0.3"/>
    
    <!-- 医生简化图标 -->
    <g transform="translate(150,100)">
      <circle cx="0" cy="-20" r="15" fill="#4caf50"/>
      <rect x="-10" y="-5" width="20" height="30" fill="#ffffff" stroke="#4caf50" stroke-width="2"/>
      <!-- 听诊器 -->
      <circle cx="0" cy="0" r="8" fill="#4caf50" opacity="0.3"/>
      <path d="M-8,0 Q-15,-10 -20,-5" stroke="#4caf50" stroke-width="3" fill="none"/>
      <path d="M8,0 Q15,-10 20,-5" stroke="#4caf50" stroke-width="3" fill="none"/>
      <!-- 病历本 -->
      <rect x="25" y="-10" width="20" height="25" fill="#ffffff" stroke="#4caf50" stroke-width="2"/>
      <line x1="30" y1="-5" x2="40" y2="-5" stroke="#4caf50" stroke-width="1"/>
      <line x1="30" y1="0" x2="40" y2="0" stroke="#4caf50" stroke-width="1"/>
      <line x1="30" y1="5" x2="40" y2="5" stroke="#4caf50" stroke-width="1"/>
    </g>
    
    <text x="300" y="250" text-anchor="middle" font-family="Microsoft YaHei" font-size="36" font-weight="bold" fill="#2e7d32">正确的保有观</text>
    
    <!-- 特点列表 -->
    <circle cx="50" cy="300" r="6" fill="#4caf50"/>
    <text x="80" y="310" font-family="Microsoft YaHei" font-size="28" fill="#424242">事前预警</text>
    
    <circle cx="50" cy="340" r="6" fill="#4caf50"/>
    <text x="80" y="350" font-family="Microsoft YaHei" font-size="28" fill="#424242">依赖体系</text>
    
    <circle cx="50" cy="380" r="6" fill="#4caf50"/>
    <text x="80" y="390" font-family="Microsoft YaHei" font-size="28" fill="#424242">过程驱动（客户健康度）</text>
  </g>
  
  <!-- 中间对比箭头 -->
  <g transform="translate(960,450)">
    <path d="M-100,0 L100,0" stroke="#ff9800" stroke-width="6" marker-end="url(#orangeArrow)"/>
    <text x="0" y="-20" text-anchor="middle" font-family="Microsoft YaHei" font-size="24" font-weight="bold" fill="#ff9800">转变</text>
  </g>
  
  <!-- 箭头标记定义 -->
  <defs>
    <marker id="orangeArrow" markerWidth="12" markerHeight="8" refX="10" refY="4" orient="auto">
      <polygon points="0 0, 12 4, 0 8" fill="#ff9800"/>
    </marker>
  </defs>
  
  <!-- 底部装饰弧线 -->
  <path d="M0,950 Q960,900 1920,950" stroke="#ffebee" stroke-width="20" fill="none" opacity="0.4"/>
</svg>
