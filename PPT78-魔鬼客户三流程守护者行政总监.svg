<?xml version="1.0" encoding="UTF-8"?>
<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="#ffffff"/>
  
  <!-- 装饰性弧线背景 -->
  <path d="M0,100 Q960,50 1920,100" stroke="#e8f5e8" stroke-width="25" fill="none" opacity="0.3"/>
  
  <!-- 标题 -->
  <text x="960" y="120" text-anchor="middle" font-family="Microsoft YaHei" font-size="48" font-weight="bold" fill="#2e7d32">
    魔鬼客户三：流程守护者 (行政总监)
  </text>
  
  <!-- 复杂流程图和钟表图标 -->
  <g transform="translate(960,300)">
    <!-- 流程图 -->
    <g transform="translate(-50,0)">
      <!-- 流程节点 -->
      <rect x="-60" y="-40" width="40" height="20" rx="5" fill="#4caf50" opacity="0.8"/>
      <text x="-40" y="-25" text-anchor="middle" font-family="Microsoft YaHei" font-size="10" fill="#ffffff">开始</text>
      
      <rect x="-60" y="-10" width="40" height="20" rx="5" fill="#2196f3" opacity="0.8"/>
      <text x="-40" y="5" text-anchor="middle" font-family="Microsoft YaHei" font-size="10" fill="#ffffff">审批</text>
      
      <rect x="-60" y="20" width="40" height="20" rx="5" fill="#ff9800" opacity="0.8"/>
      <text x="-40" y="35" text-anchor="middle" font-family="Microsoft YaHei" font-size="10" fill="#ffffff">执行</text>
      
      <rect x="20" y="-40" width="40" height="20" rx="5" fill="#9c27b0" opacity="0.8"/>
      <text x="40" y="-25" text-anchor="middle" font-family="Microsoft YaHei" font-size="10" fill="#ffffff">协调</text>
      
      <rect x="20" y="-10" width="40" height="20" rx="5" fill="#f44336" opacity="0.8"/>
      <text x="40" y="5" text-anchor="middle" font-family="Microsoft YaHei" font-size="10" fill="#ffffff">反馈</text>
      
      <rect x="20" y="20" width="40" height="20" rx="5" fill="#607d8b" opacity="0.8"/>
      <text x="40" y="35" text-anchor="middle" font-family="Microsoft YaHei" font-size="10" fill="#ffffff">完成</text>
      
      <!-- 连接线 -->
      <g stroke="#424242" stroke-width="2" fill="none">
        <line x1="-20" y1="-30" x2="20" y2="-30"/>
        <line x1="-40" y1="-20" x2="-40" y2="-10"/>
        <line x1="-20" y1="0" x2="20" y2="0"/>
        <line x1="-40" y1="10" x2="-40" y2="20"/>
        <line x1="-20" y1="30" x2="20" y2="30"/>
        <line x1="40" y1="-20" x2="40" y2="-10"/>
        <line x1="40" y1="10" x2="40" y2="20"/>
      </g>
      
      <!-- 箭头 -->
      <polygon points="18,-32 22,-30 18,-28" fill="#424242"/>
      <polygon points="18,-2 22,0 18,2" fill="#424242"/>
      <polygon points="18,28 22,30 18,32" fill="#424242"/>
    </g>
    
    <!-- 钟表 -->
    <g transform="translate(50,0)">
      <circle cx="0" cy="0" r="40" fill="#37474f" opacity="0.8"/>
      <circle cx="0" cy="0" r="35" fill="#ffffff"/>
      
      <!-- 时钟刻度 -->
      <g stroke="#37474f" stroke-width="2">
        <line x1="0" y1="-30" x2="0" y2="-25"/>
        <line x1="30" y1="0" x2="25" y2="0"/>
        <line x1="0" y1="30" x2="0" y2="25"/>
        <line x1="-30" y1="0" x2="-25" y2="0"/>
      </g>
      
      <!-- 时钟指针 -->
      <line x1="0" y1="0" x2="0" y2="-20" stroke="#f44336" stroke-width="3"/>
      <line x1="0" y1="0" x2="15" y2="0" stroke="#f44336" stroke-width="2"/>
      <circle cx="0" cy="0" r="3" fill="#f44336"/>
      
      <!-- 数字 -->
      <text x="0" y="-18" text-anchor="middle" font-family="Microsoft YaHei" font-size="12" fill="#37474f">12</text>
      <text x="18" y="5" text-anchor="middle" font-family="Microsoft YaHei" font-size="12" fill="#37474f">3</text>
      <text x="0" y="22" text-anchor="middle" font-family="Microsoft YaHei" font-size="12" fill="#37474f">6</text>
      <text x="-18" y="5" text-anchor="middle" font-family="Microsoft YaHei" font-size="12" fill="#37474f">9</text>
    </g>
  </g>
  
  <!-- 口头禅 -->
  <g transform="translate(960,450)">
    <rect x="-400" y="-40" width="800" height="80" rx="15" fill="#e8f5e8" opacity="0.3"/>
    <text x="0" y="-10" text-anchor="middle" font-family="Microsoft YaHei" font-size="24" font-weight="bold" fill="#2e7d32">她的口头禅：</text>
    <text x="0" y="25" text-anchor="middle" font-family="Microsoft YaHei" font-size="28" font-weight="bold" fill="#d32f2f">"别给我找麻烦就行。"</text>
  </g>
  
  <!-- 她关心的问题 -->
  <g transform="translate(960,650)">
    <rect x="-700" y="-150" width="1400" height="300" rx="20" fill="#e8f5e8" opacity="0.3"/>
    <text x="0" y="-110" text-anchor="middle" font-family="Microsoft YaHei" font-size="32" font-weight="bold" fill="#2e7d32">她关心的问题</text>
    
    <g transform="translate(-500,-50)">
      <circle cx="0" cy="0" r="6" fill="#2e7d32"/>
      <text x="20" y="5" font-family="Microsoft YaHei" font-size="18" fill="#424242">"听起来是很美好，但实施起来，会不会把我们公司搞得鸡飞狗跳？"</text>
    </g>
    
    <g transform="translate(-500,-10)">
      <circle cx="0" cy="0" r="6" fill="#2e7d32"/>
      <text x="20" y="5" font-family="Microsoft YaHei" font-size="18" fill="#424242">"需要我们多少个部门配合？需要我们的员工花多长时间来学习和适应？"</text>
    </g>
    
    <g transform="translate(-500,30)">
      <circle cx="0" cy="0" r="6" fill="#2e7d32"/>
      <text x="20" y="5" font-family="Microsoft YaHei" font-size="18" fill="#424242">"会不会影响我们正常的办公秩序？如果员工不配合，你们有什么办法？"</text>
    </g>
    
    <g transform="translate(-500,70)">
      <circle cx="0" cy="0" r="6" fill="#2e7d32"/>
      <text x="20" y="5" font-family="Microsoft YaHei" font-size="18" fill="#424242">"整个流程，你们能提供一个'交钥匙'工程吗？我可不想天天跟在你们后面擦屁股。"</text>
    </g>
  </g>
  
  <!-- 装饰性流程图标 -->
  <g transform="translate(200,400)" opacity="0.4">
    <rect x="0" y="0" width="20" height="15" rx="3" fill="#2e7d32"/>
    <rect x="25" y="0" width="20" height="15" rx="3" fill="#4caf50"/>
    <line x1="20" y1="7" x2="25" y2="7" stroke="#424242" stroke-width="2"/>
    <polygon points="23,5 25,7 23,9" fill="#424242"/>
  </g>
  
  <g transform="translate(1720,400)" opacity="0.4">
    <rect x="-45" y="0" width="20" height="15" rx="3" fill="#2e7d32"/>
    <rect x="-20" y="0" width="20" height="15" rx="3" fill="#4caf50"/>
    <line x1="-25" y1="7" x2="-20" y2="7" stroke="#424242" stroke-width="2"/>
    <polygon points="-22,5 -20,7 -22,9" fill="#424242"/>
  </g>
</svg>
