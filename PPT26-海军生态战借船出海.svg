<?xml version="1.0" encoding="UTF-8"?>
<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="#ffffff"/>
  
  <!-- 装饰性弧线背景 -->
  <path d="M0,100 Q960,50 1920,100" stroke="#e1f5fe" stroke-width="25" fill="none" opacity="0.3"/>
  
  <!-- 标题 -->
  <text x="960" y="120" text-anchor="middle" font-family="Microsoft YaHei" font-size="56" font-weight="bold" fill="#0277bd">
    海军·生态战：学会"借船出海"
  </text>
  
  <!-- 两艘大船互相靠近 -->
  <g transform="translate(960,300)">
    <!-- 左船 -->
    <g transform="translate(-200,0)">
      <ellipse cx="0" cy="0" rx="80" ry="30" fill="#4caf50" opacity="0.8"/>
      <rect x="-70" y="-40" width="140" height="20" fill="#2e7d32"/>
      <rect x="-30" y="-50" width="8" height="20" fill="#2e7d32"/>
      <rect x="-10" y="-55" width="8" height="25" fill="#2e7d32"/>
      <rect x="10" y="-50" width="8" height="20" fill="#2e7d32"/>
      <text x="0" y="50" text-anchor="middle" font-family="Microsoft YaHei" font-size="20" font-weight="bold" fill="#2e7d32">我方</text>
    </g>
    
    <!-- 右船 -->
    <g transform="translate(200,0)">
      <ellipse cx="0" cy="0" rx="80" ry="30" fill="#2196f3" opacity="0.8"/>
      <rect x="-70" y="-40" width="140" height="20" fill="#1976d2"/>
      <rect x="-30" y="-50" width="8" height="20" fill="#1976d2"/>
      <rect x="-10" y="-55" width="8" height="25" fill="#1976d2"/>
      <rect x="10" y="-50" width="8" height="20" fill="#1976d2"/>
      <text x="0" y="50" text-anchor="middle" font-family="Microsoft YaHei" font-size="20" font-weight="bold" fill="#1976d2">合作伙伴</text>
    </g>
    
    <!-- 资源交换箭头 -->
    <path d="M-100,-20 L100,-20" stroke="#ff9800" stroke-width="4" marker-end="url(#orangeArrow)"/>
    <path d="M100,20 L-100,20" stroke="#ff9800" stroke-width="4" marker-end="url(#orangeArrow)"/>
    <text x="0" y="-40" text-anchor="middle" font-family="Microsoft YaHei" font-size="24" font-weight="bold" fill="#ff9800">资源交换</text>
  </g>
  
  <!-- 模式一：异业联盟 -->
  <g transform="translate(300,500)">
    <rect x="0" y="0" width="600" height="300" rx="20" fill="#e8f5e8" opacity="0.3"/>
    <text x="300" y="40" text-anchor="middle" font-family="Microsoft YaHei" font-size="32" font-weight="bold" fill="#2e7d32">模式一：异业联盟，权益互换</text>
    
    <text x="50" y="80" font-family="Microsoft YaHei" font-size="24" font-weight="bold" fill="#2e7d32">伙伴：</text>
    <text x="120" y="80" font-family="Microsoft YaHei" font-size="22" fill="#424242">员工"1公里生活圈"的高频消费场所</text>
    <text x="120" y="110" font-family="Microsoft YaHei" font-size="22" fill="#424242">（咖啡馆、商超、电影院、健身房…）</text>
    
    <text x="50" y="150" font-family="Microsoft YaHei" font-size="24" font-weight="bold" fill="#2e7d32">玩法：</text>
    
    <circle cx="80" cy="180" r="6" fill="#4caf50"/>
    <text x="100" y="190" font-family="Microsoft YaHei" font-size="20" fill="#424242">权益置换：我们的积分换他的咖啡券。</text>
    
    <circle cx="80" cy="210" r="6" fill="#4caf50"/>
    <text x="100" y="220" font-family="Microsoft YaHei" font-size="20" fill="#424242">联合会员：办我们的套餐，就是他的VIP。</text>
    
    <circle cx="80" cy="240" r="6" fill="#4caf50"/>
    <text x="100" y="250" font-family="Microsoft YaHei" font-size="20" fill="#424242">渠道互驻：在他的店里，也能办我们的卡。</text>
  </g>
  
  <!-- 模式二：B2B2C -->
  <g transform="translate(1020,500)">
    <rect x="0" y="0" width="600" height="300" rx="20" fill="#e3f2fd" opacity="0.3"/>
    <text x="300" y="40" text-anchor="middle" font-family="Microsoft YaHei" font-size="32" font-weight="bold" fill="#1565c0">模式二：B2B2C，渠道赋能</text>
    
    <text x="50" y="80" font-family="Microsoft YaHei" font-size="24" font-weight="bold" fill="#1565c0">伙伴：</text>
    <text x="120" y="80" font-family="Microsoft YaHei" font-size="22" fill="#424242">产业园区物业、写字楼管理方、</text>
    <text x="120" y="110" font-family="Microsoft YaHei" font-size="22" fill="#424242">大型企业供应商…</text>
    
    <text x="50" y="150" font-family="Microsoft YaHei" font-size="24" font-weight="bold" fill="#1565c0">玩法：</text>
    
    <circle cx="80" cy="180" r="6" fill="#2196f3"/>
    <text x="100" y="190" font-family="Microsoft YaHei" font-size="20" fill="#424242">我们为"大B"（如物业）提供智慧园区解决方案。</text>
    
    <circle cx="80" cy="210" r="6" fill="#2196f3"/>
    <text x="100" y="220" font-family="Microsoft YaHei" font-size="20" fill="#424242">"大B"为我们提供向"小B"（园区内企业）营销的</text>
    <text x="100" y="240" font-family="Microsoft YaHei" font-size="20" fill="#424242">独家或优先权。</text>
    
    <circle cx="80" cy="270" r="6" fill="#2196f3"/>
    <text x="100" y="280" font-family="Microsoft YaHei" font-size="20" fill="#424242">从"一家家敲门"，变为"房东带着进门"。</text>
  </g>
  
  <!-- 底部核心理念 -->
  <g transform="translate(960,900)">
    <rect x="-500" y="-50" width="1000" height="100" rx="15" fill="#fff3e0" opacity="0.3"/>
    <text x="0" y="-10" text-anchor="middle" font-family="Microsoft YaHei" font-size="32" font-weight="bold" fill="#f57c00">
      海军核心理念：跳出企业小圈子，整合1公里生活圈资源
    </text>
    <text x="0" y="25" text-anchor="middle" font-family="Microsoft YaHei" font-size="24" fill="#424242">
      找到能"批量"管着目标客户的"大B"，实现渠道赋能和生态共赢
    </text>
  </g>
  
  <!-- 箭头标记定义 -->
  <defs>
    <marker id="orangeArrow" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#ff9800"/>
    </marker>
  </defs>
</svg>
